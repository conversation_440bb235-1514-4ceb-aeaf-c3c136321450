#!/usr/bin/env node

/**
 * 修复可选链操作符的脚本
 * 将项目中所有的可选链操作符 (?.) 替换为兼容的语法
 * 使用方法: node scripts/fix-optional-chaining.js
 */

const fs = require('fs');
const path = require('path');

// 需要检查的文件扩展名
const FILE_EXTENSIONS = ['.js'];

// 需要排除的目录
const EXCLUDE_DIRS = [
  'node_modules',
  '.history',
  '.git',
  'dist',
  'build'
];

/**
 * 检查文件是否应该被处理
 */
function shouldProcessFile(filePath) {
  // 检查文件扩展名
  const ext = path.extname(filePath);
  if (!FILE_EXTENSIONS.includes(ext)) {
    return false;
  }

  // 检查是否在排除目录中
  const relativePath = path.relative(process.cwd(), filePath);
  for (const excludeDir of EXCLUDE_DIRS) {
    if (relativePath.startsWith(excludeDir + path.sep) || relativePath.startsWith(excludeDir + '/')) {
      return false;
    }
  }

  return true;
}

/**
 * 递归获取所有需要处理的文件
 */
function getAllFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // 检查是否是排除目录
        if (!EXCLUDE_DIRS.includes(item)) {
          traverse(fullPath);
        }
      } else if (stat.isFile() && shouldProcessFile(fullPath)) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

/**
 * 修复可选链操作符的替换规则
 */
const OPTIONAL_CHAINING_PATTERNS = [
  // obj?.prop 替换为 obj && obj.prop
  {
    pattern: /(\w+)\?\.([\w\[\]'"]+)/g,
    replacement: '$1 && $1.$2'
  },
  // obj?.method() 替换为 obj && obj.method()
  {
    pattern: /(\w+)\?\.(\w+\([^)]*\))/g,
    replacement: '$1 && $1.$2'
  },
  // obj?.prop?.subprop 的复杂情况需要特殊处理
  {
    pattern: /(\w+)\?\.([\w\[\]'"]+)\?\.([\w\[\]'"]+)/g,
    replacement: '$1 && $1.$2 && $1.$2.$3'
  },
  // callback?.() 替换为 callback && callback()
  {
    pattern: /(\w+)\?\.\(\)/g,
    replacement: '$1 && $1()'
  },
  // callback?.(args) 替换为 callback && callback(args)
  {
    pattern: /(\w+)\?\.\(([^)]*)\)/g,
    replacement: '$1 && $1($2)'
  }
];

/**
 * 修复文件中的可选链操作符
 */
function fixOptionalChaining(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let hasChanges = false;
  let changeCount = 0;

  // 应用所有替换规则
  for (const rule of OPTIONAL_CHAINING_PATTERNS) {
    const matches = content.match(rule.pattern);
    if (matches) {
      content = content.replace(rule.pattern, rule.replacement);
      changeCount += matches.length;
      hasChanges = true;
    }
  }

  if (hasChanges) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ 已修复 ${path.relative(process.cwd(), filePath)} - 替换了 ${changeCount} 处`);
    return true;
  }

  return false;
}

/**
 * 检查文件是否包含可选链操作符
 */
function checkForOptionalChaining(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const optionalChainingRegex = /\?\./g;
  const matches = content.match(optionalChainingRegex);
  
  if (matches) {
    console.log(`⚠️  发现可选链操作符: ${path.relative(process.cwd(), filePath)} (${matches.length} 处)`);
    return true;
  }
  
  return false;
}

/**
 * 主函数
 */
function main() {
  const args = process.argv.slice(2);
  const isCheckMode = args.includes('--check');
  
  console.log('🔍 扫描项目中的可选链操作符...\n');

  const files = getAllFiles(process.cwd());
  let totalFiles = 0;
  let affectedFiles = 0;

  for (const filePath of files) {
    totalFiles++;
    
    if (isCheckMode) {
      if (checkForOptionalChaining(filePath)) {
        affectedFiles++;
      }
    } else {
      if (fixOptionalChaining(filePath)) {
        affectedFiles++;
      }
    }
  }

  console.log(`\n📊 扫描完成:`);
  console.log(`   总文件数: ${totalFiles}`);
  
  if (isCheckMode) {
    console.log(`   包含可选链操作符的文件: ${affectedFiles}`);
    if (affectedFiles > 0) {
      console.log('\n💡 运行 `node scripts/fix-optional-chaining.js` 来修复这些问题');
    } else {
      console.log('\n✅ 没有发现可选链操作符，项目兼容性良好！');
    }
  } else {
    console.log(`   已修复的文件: ${affectedFiles}`);
    console.log(`   无需修复的文件: ${totalFiles - affectedFiles}`);
    
    if (affectedFiles > 0) {
      console.log('\n⚠️  注意事项:');
      console.log('1. 请检查修复后的代码逻辑是否正确');
      console.log('2. 建议运行测试确保功能正常');
      console.log('3. 可以使用 git diff 查看具体更改');
    } else {
      console.log('\n✅ 没有发现需要修复的可选链操作符！');
    }
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { 
  fixOptionalChaining, 
  checkForOptionalChaining, 
  OPTIONAL_CHAINING_PATTERNS 
};
