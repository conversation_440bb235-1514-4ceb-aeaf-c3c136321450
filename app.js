App({
  globalData: {
    logoutTimer: null,
    backgroundEnterTime: null  // 添加记录进入后台的时间
  },
  onLaunch(options) {
    // Page opens for the first time
    console.info('App onLaunch');
    // const backgroundTime = Date.now() - this.globalData.backgroundEnterTime;

    // 如果后台时间超过3分钟，清除token
    // 3 * 60 * 1000
    // if (backgroundTime >= 10 * 1000) {
    //   console.log('后台时间超过3分钟，清除token');
    //   my.removeStorage({
    //     key: 'token',
    //     success: () => {
    //       console.log('token已清除');
    //     }
    //   });
    // }

    // this.globalData.backgroundEnterTime = null;
  },
  onShow(options) {
    
  },
  onHide() {
    console.log('小程序进入后台');
    // 记录进入后台的时间
    // this.globalData.backgroundEnterTime = Date.now();
  },
  onUnload() {
    console.log('App onUnload')
  }
});
