import {
  httpClient
} from "./http.js";
import {
  APP_GCASH,
  APP_VERSION,
  APP_DEVICE,
  APP_DEFAULT_DEVICE_ID
} from "./config.js";
import {
  getDeviceId
} from "./storage-manager.js";

/**
 * 高级请求管理器
 * 提供统一的错误处理、token管理、重试机制等功能
 */
class RequestManager {
  constructor() {
    this.retryCount = 0;
    this.maxRetries = 3;
    this.isLoginLoading = false;

    // 错误码映射
    this.errorCodes = {
      TOKEN_EXPIRED: [401, 400, 100010],
      SYSTEM_MAINTENANCE: [102121],
      NETWORK_ERROR: [102020],
      IP_RESTRICTED: [1000051],
      ACCOUNT_LOCKED: [102008],
      ACCOUNT_BLACKLIST: [101013],
      UNDER_AGE: [102043],
    };
  }

  /**
   * 统一的请求方法，包含错误处理和重试机制
   * @param {Object} options - 请求配置
   * @param {Object} [context] - 页面上下文，用于错误处理
   * @returns {Promise}
   */
  async request(options, context = null) {
    try {
      const response = await httpClient.request(options);

      // 检查业务错误码
      if (response.data && response.data.code) {
        const errorType = this.checkErrorCode(response.data.code);
        if (errorType && context) {
          return this.handleError(errorType, response.data, context);
        }
      }

      return response;
    } catch (error) {
      // 网络错误处理
      if (context) {
        return this.handleNetworkError(error, context);
      }
      throw error;
    }
  }

  /**
   * 检查错误码类型
   * @param {number} code - 错误码
   * @returns {string|null} 错误类型
   */
  checkErrorCode(code) {
    for (const [errorType, codes] of Object.entries(this.errorCodes)) {
      if (codes.includes(code)) {
        return errorType;
      }
    }
    return null;
  }

  /**
   * 处理业务错误
   * @param {string} errorType - 错误类型
   * @param {Object} data - 响应数据
   * @param {Object} context - 页面上下文
   */
  handleError(errorType, data, context) {
    switch (errorType) {
      case 'TOKEN_EXPIRED':
        return this.handleTokenExpired(context);

      case 'SYSTEM_MAINTENANCE':
        return this.handleSystemMaintenance(context);

      case 'IP_RESTRICTED':
        return this.handleIpRestricted(context);

      case 'ACCOUNT_LOCKED':
      case 'ACCOUNT_BLACKLIST':
        return this.handleAccountLocked(data, context);

      case 'UNDER_AGE':
        return this.handleUnderAge(context);

      default:
        return {
          success: false, errorType, data
        };
    }
  }

  /**
   * 处理token失效
   */
  handleTokenExpired(context) {
    // 清除token
    my.removeStorage({
      key: "token"
    });

    // 使用指数退避策略
    const backoffTime = Math.min(1000 * Math.pow(2, this.retryCount), 10000);

    // 显示加载提示
    my.showLoading({
      content: "Reconnecting..."
    });

    this.retryCount++;

    // 延迟执行重试
    setTimeout(() => {
      my.hideLoading();
      if (context && typeof context.getAuthCode === 'function') {
        context.getAuthCode();
      }
    }, backoffTime);

    return {
      success: false,
      errorType: 'TOKEN_EXPIRED'
    };
  }

  /**
   * 处理系统维护
   */
  handleSystemMaintenance(context) {
    if (context && typeof context.setData === 'function') {
      context.setData({
        showMessageConfrim: true,
        showMessageTitle: "System Maintenance",
        showMessagePagrm: "Since you have not operated on for a long time, please log in again.",
      });
    }
    return {
      success: false,
      errorType: 'SYSTEM_MAINTENANCE'
    };
  }

  /**
   * 处理IP限制
   */
  handleIpRestricted(context) {
    if (context && typeof context.setData === 'function') {
      context.setData({
        ip_restricted: true
      });
    }
    return {
      success: false,
      errorType: 'IP_RESTRICTED'
    };
  }

  /**
   * 处理账号锁定
   */
  handleAccountLocked(data, context) {
    if (context && typeof context.setData === 'function') {
      context.setData({
        account_locked: true,
        account_locked_msg: data.msg,
        customer_email: data.data ? data.data.customer_email : "",
      });
    }
    return {
      success: false,
      errorType: 'ACCOUNT_LOCKED',
      data
    };
  }

  /**
   * 处理未满21岁
   */
  handleUnderAge(context) {
    if (context && typeof context.setData === 'function') {
      context.setData({
        forbid_access_no_button: true
      });
    }
    return {
      success: false,
      errorType: 'UNDER_AGE'
    };
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(error, context) {
    console.error('Network error:', error);

    my.showToast({
      type: "fail",
      content: "Network connection error, please try again",
      duration: 2000,
    });

    return {
      success: false,
      errorType: 'NETWORK_ERROR',
      error
    };
  }

  /**
   * 登录请求
   * @param {string} authCode - 授权码
   * @param {Object} context - 页面上下文
   */
  async login(authCode, context) {
    if (this.isLoginLoading) {
      return {
        success: false,
        message: 'Login in progress'
      };
    }

    this.isLoginLoading = true;

    try {
      const response = await this.request({
        url: "api/player/login",
        baseUrl: "common",
        method: "POST",
        data: {
          app_package_name: APP_GCASH.packageName,
          appPackageName: APP_GCASH.packageName,
          app_version: APP_VERSION,
          appVersion: APP_VERSION,
          device_id: getDeviceId() || APP_DEFAULT_DEVICE_ID,
          gcash_auth_code: authCode,
          market_channel: context && context.data ? context.data.marketChannel : "",
          login_type: "gcash",
          appChannel: APP_GCASH.channel,
          deviceModel: APP_DEVICE.model,
          deviceVersion: APP_DEVICE.version,
          sysLanguage: APP_DEVICE.language,
          sysTimezone: APP_DEVICE.timezone,
          source: APP_DEVICE.source,
          isNative: APP_DEVICE.isNative,
          telephoneCode: APP_GCASH.telephoneCode,
          registration_channel: APP_GCASH.registrationChannel,
          action: "/common/api/player/login",
        },
        showLoading: true,
      }, context);

      this.isLoginLoading = false;

      if (response.data && response.data.code === 200) {
        this.retryCount = 0; // 重置重试计数
        return {
          success: true,
          data: response.data.data
        };
      } else {
        return {
          success: false,
          data: response.data
        };
      }
    } catch (error) {
      this.isLoginLoading = false;
      throw error;
    }
  }

  /**
   * 获取用户信息
   * @param {string} token - 用户token
   * @param {Object} context - 页面上下文
   */
  async getUserInfo(token, context) {
    return this.request({
      url: "api/player/info",
      baseUrl: "common",
      method: "POST",
      token,
      showLoading: true,
    }, context);
  }

  /**
   * 重置重试计数
   */
  resetRetryCount() {
    this.retryCount = 0;
  }

  /**
   * 检查是否达到最大重试次数
   */
  isRetryMaxReached() {
    return this.retryCount > this.maxRetries;
  }
}

// 创建单例实例
const requestManager = new RequestManager();

export {
  requestManager,
  RequestManager
};
export default requestManager;