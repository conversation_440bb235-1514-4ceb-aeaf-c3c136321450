import { httpClient } from "/utils/http";
import { AWARD_NAME_OBJECTS } from "/utils/types";
import { formatStatusName } from "/utils/common-utils";

/**
 * 弹窗管理器类 - 负责管理活动弹窗的显示顺序、优先级和生命周期
 */
export default class PopupManager {
  constructor(info) {
    if (!info || !info.token) {
      throw new Error("PopupManager requires user info with token");
    }

    // 弹窗队列和状态
    this.popupQueue = [];
    this.currentPopup = null;
    this.isShowing = false;

    // 用户信息
    this.token = info.token;
    this.isVip = Boolean(info.isVip);
    this.register_award = info.register_award || null;
    this.is_register = info.is_register || 0;
    this.awardTypes = Array.isArray(info.awardTypes) ? info.awardTypes : [];

    // 回调函数
    this.popupChangeCallback = null;
  }

  /**
   * 默认错误处理器
   * @param {Error} error - 错误对象
   * @param {string} context - 错误上下文
   */
  handleError(error, context = "PopupManager") {
    console.error(`[${context}] Error:`, error);
  }

  /**
   * 设置弹窗变化回调函数
   */
  setPopupChangeCallback(callback) {
    if (typeof callback === "function") {
      this.popupChangeCallback = callback;
    }
  }

  /**
   * 初始化弹窗队列
   * 按顺序检查各种弹窗条件并构建显示队列
   */
  async initialize() {
    try {
      // 1. 检查本地弹窗条件
      await this.checkLocalPopups();
      // 2. 获取服务端活动弹窗\获取调账之后的玩家奖励记录弹窗
      await Promise.all([this.fetchServerPopups(), this.getBonusPopups()]);

      // 3. 排序弹窗队列
      this.sortPopupQueue();
      // 4. 开始显示弹窗
      this.showNextPopup();
    } catch (error) {
      this.handleError(error, "initialize");
    }
  }
  /**
   * 获取调账后奖励弹窗
   */
  async getBonusPopups() {
    try {
      const response = await httpClient.get("open/api/activity/adjustment_bonus_record/list", {
        baseUrl: "main",
        token: this.token,
      });

      const bonusPopups = (response.data && response.data.data && response.data.data.list) || [];

      // // mockData
      // const bonusPopups = [{
      //   "id": "86584224571682824", // 奖励记录id
      //   "activity_name": "Casino Elite Wealth Leaderboard", //副标题
      //   "bonus": "88.33", //金额，单位：元
      //   "bonus_date": "2025/05/07" //奖励日期
      // }, {
      //   "id": "86584224571682824", // 奖励记录id
      //   "activity_name": "Late Night Cashback", //副标题
      //   "bonus": "188.33", //金额，单位：元
      //   "bonus_date": "2025/05/07" //奖励日期
      // }]

      bonusPopups.forEach((bonus) => {
        bonus.activity_name = formatStatusName(bonus.activity_name);
        this.addPopup({
          id: "BonusPopups",
          name: "BonusPopups",
          type: "bonus_popups",
          priority: 10,
          data: bonus,
        });
      });
    } catch (error) {
      this.handleError(error, "getBonusPopups");
    }
  }

  /**
   * 检查本地弹窗条件
   */
  async checkLocalPopups() {
    // 年龄验证弹窗
    if (this.is_register !== 0) {
      this.addPopup({
        id: "Tip21Old",
        name: "Tip21Old",
        type: "age_verification",
        priority: 13,
        data: null,
      });
    }

    // VIP提示弹窗
    if (this.isVip && (await this.checkVipStatus())) {
      this.addPopup({
        id: "VipTip",
        name: "VipTip",
        type: "Bet",
        priority: 12,
        data: { type: "VipTip", id: "123" },
      });
    }

    // 注册奖励弹窗
    if (this.register_award && [1, 2, 3].includes(this.register_award.type) && +this.register_award.amount > 0) {
      this.addPopup({
        id: "RegisterBonus",
        name: "Register Bonus",
        type: "Bet",
        priority: 5,
        data: {
          type: "RegisterBonus",
          amount: this.register_award.amount,
        },
      });
    }
  }
  /**
   * 检查VIP状态 - VIP状态每月1-15号检查一次，16-30号检查一次
   */
  async checkVipStatus() {
    const day = new Date().getDate();

    try {
      const { data: hasUpChecked } = (await my.getStorage({ key: "vip_status_checked_up" })) || {};
      const { data: hasDownChecked } = (await my.getStorage({ key: "vip_status_checked_down" })) || {};

      // 1-15号检查
      if (day >= 1 && day <= 15 && !hasUpChecked) {
        my.setStorage({ key: "vip_status_checked_up", data: true });
        my.setStorage({ key: "vip_status_checked_down", data: false });
        return true;
      }

      // 16-30号检查
      if (day >= 16 && day <= 31 && !hasDownChecked) {
        my.setStorage({ key: "vip_status_checked_down", data: true });
        my.setStorage({ key: "vip_status_checked_up", data: false });
        return true;
      }
    } catch (error) {
      this.handleError(error, "checkVipStatus");
    }

    return false;
  }

  /**
   * 获取服务端活动弹窗
   */
  async fetchServerPopups() {
    try {
      const response = await httpClient.post("api/activity/bonus_list", { token: this.token }, { baseUrl: "avt" });
      const activityPopups = (response.data && response.data.data && response.data.data.list) || [];

      activityPopups.forEach((item) => {
        let type_name = AWARD_NAME_OBJECTS[item.type] || "";
        let priority = 4;

        // 特殊类型处理
        if (item.type === 111) {
          priority = 10;
          type_name = "Daily Rebate";
        } else if ([119, 241].includes(item.type)) {
          priority = 11;
          type_name = "Vip Rebate";
        }

        // 使用后台配置的名称
        const awardType = this.awardTypes.find((t) => t.change_type === item.type);
        if (awardType) {
          type_name = formatStatusName(awardType.title);
        }

        if (type_name) {
          this.addPopup({
            id: "ActivityBonus",
            name: type_name,
            type: type_name || "ActivityBonus",
            priority: priority,
            data: item,
          });
        }
      });
    } catch (error) {
      this.handleError(error, "fetchServerPopups");
    }
  }
  /**
   * 添加弹窗到队列
   */
  addPopup(popup) {
    this.popupQueue.push(popup);
  }

  /**
   * 按优先级排序弹窗队列
   */
  sortPopupQueue() {
    this.popupQueue.sort((a, b) => b.priority - a.priority);
  }

  /**
   * 显示下一个弹窗
   */
  showNextPopup() {
    if (this.isShowing || this.popupQueue.length === 0) {
      if (this.popupChangeCallback) {
        this.popupChangeCallback(null);
      }
      return false;
    }

    this.currentPopup = this.popupQueue.shift();
    this.isShowing = true;
    if (this.popupChangeCallback) {
      this.popupChangeCallback(this.currentPopup);
    }
    return true;
  }

  /**
   * 关闭当前弹窗
   */
  async closeCurrentPopup(type) {
    await this.handlePopupResult(this.currentPopup, type);
    this.isShowing = false;
    this.currentPopup = null;
    return this.showNextPopup();
  }

  /**
   * 处理弹窗结果
   */
  async handlePopupResult(popup, { type }) {
    if (!popup) return;

    if (type === "claim") {
      try {
        if (popup.name === "BonusPopups") {
          await this.claimBonusPopup(popup);
        } else {
          await this.claimAward(popup);
        }
      } catch (error) {
        this.handleError(error, "handlePopupResult");
      }
    }
  }

  /**
   * 领取调账奖励
   */
  async claimBonusPopup(popup) {
    return httpClient.post("open/api/activity/adjustment_bonus_record/receive", { id: popup.data.id, token: this.token }, { baseUrl: "main", token: this.token });
  }

  /**
   * 领取活动奖励
   */
  async claimAward(popup) {
    return httpClient.post("api/activity/bonus_receive", { token: this.token, id: popup.data.id, type: popup.data.type }, { baseUrl: "avt" });
  }

  /**
   * 获取当前弹窗信息
   */
  getCurrentPopup() {
    return this.currentPopup;
  }

  /**
   * 检查是否有正在显示的弹窗
   */
  hasActivePopup() {
    return this.isShowing;
  }
}
