// loading-progress/loading-progress.js
Component({
  properties: {
    progress: {
      type: Number,
      value: 0,
      observer: function(newVal) {
        this.updateProgress(newVal);
      }
    }
  },

  data: {
    leftMaskStyle: '',
    rightMaskStyle: ''
  },

  methods: {
    updateProgress(progress) {
      // 限制进度范围在0-100之间
      progress = Math.min(100, Math.max(0, progress));
      const degree = progress * 3.6; // 将百分比转换为角度 (360 * progress / 100)
      
      if (degree <= 180) {
        // 右边遮罩固定，左边遮罩旋转
        this.setData({
          leftMaskStyle: `transform: rotate(${-180 + degree}deg)`,
          rightMaskStyle: 'transform: rotate(-180deg)'
        });
      } else {
        // 左边遮罩固定，右边遮罩旋转
        this.setData({
          leftMaskStyle: 'transform: rotate(0deg)',
          rightMaskStyle: `transform: rotate(${-360 + degree}deg)`
        });
      }
    }
  },

  lifetimes: {
    attached() {
      this.updateProgress(this.properties.progress);
    }
  }
});