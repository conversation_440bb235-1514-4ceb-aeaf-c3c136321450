function throttleFirst(func, limit) {
  let lastExec = 0;
  let timeout;

  const reset = () => {
    lastExec = 0;
    clearTimeout(timeout);
  };

  const throttled = function (...args) {
    const now = Date.now();
    if (now - lastExec >= limit) {
      func.apply(this, args);
      lastExec = now;
      // 2秒后自动重置
      timeout = setTimeout(reset, limit);
    }
  };

  throttled.reset = reset;
  return throttled;
}

export default {
  throttleFirst
}