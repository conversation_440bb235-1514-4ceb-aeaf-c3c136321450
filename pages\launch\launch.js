import { baseUrl_common, baseUrl_avt, assetsUrl, geoUrl, enableIpValidation, enableDebugMode, APP_LAUNCH, APP_VERSION, APP_DEVICE } from "/utils/config.js";
import apiService from "/utils/api-service.js";

Page({
  data: {
    app_version: APP_VERSION,
    // URL 配置从 utils/config.js 导入
    baseUrl: baseUrl_common,
    baseUrl_avt: baseUrl_avt,
    assetsUrl: assetsUrl,

    progressNum: 0,
    pro: 0,
    progressValue: 0,
    showMessageConfrim: false,
    ip_restricted: false,
    gameTypeDone: false,
    ipVaildDone: false,
    showMessageTitle: "",
    showMessagePagrm: "",
    progress: 0,
    canvas: {
      dpr: 1,
      canvasSize: 100,
      radius: 30,
      canvasWidth: 0,
    },
  },
  confrimMask() {
    // 退出小程序

    this.setData({
      showMessageConfrim: false,
    });
  },
  offConfrimMask() {
    this.setData({
      showMessageConfrim: false,
      showMessageTitle: "",
      showMessagePagrm: "",
    });
  },
  // 退出小程序
  exitMiniProgram() {
    this.setData({
      showMessageConfrim: false,
      showMessageTitle: "",
      showMessagePagrm: "",
    });
    my.exitMiniProgram();
  },
  // 验证IP
  validateIp() {
    my.request({
      url: geoUrl,
      method: "GET",
      headers: {
        terminal: APP_DEVICE.terminal,
      },
      dataType: "text",
      success: (res) => {
        console.log(res);
        if (res.data == "OK") {
          // 开始加载数据
          this.setData({
            ipVaildDone: true,
          });
          this.loadingDone();
          return;
        }

        const data = JSON.parse(res.data);
        if (data.code == 1000051) {
          this.setData({
            ip_restricted: true,
          });
        }
      },
      fail: (res) => {
        console.log(res);
        if (res.data == "OK") {
          this.setData({
            ipVaildDone: true,
          });
          this.loadingDone();
        }
        const data = JSON.parse(res.data);
        if (data.code == 1000051) {
          this.setData({
            ip_restricted: true,
          });
        }
      },
    });
  },

  capitalizeGameType(arr) {
    if (!Array.isArray(arr)) {
      console.error("Input must be an array.");
      return [];
    }

    return arr.map((item) => {
      const newItem = {
        ...item,
      }; // 创建浅拷贝，避免修改原对象
      if (newItem && newItem.game_type && typeof newItem.game_type === "string") {
        newItem.game_name = newItem.game_type.charAt(0).toUpperCase() + newItem.game_type.slice(1).toLowerCase();
      }
      return newItem;
    });
  },
  sortBySortAsc(arr) {
    return arr.sort((a, b) => {
      const aSort = typeof a.sort === "number" ? a.sort : Infinity; // 不存在或非数字的排在后面
      const bSort = typeof b.sort === "number" ? b.sort : Infinity;
      return bSort - aSort;
    });
  },
  async getGameType() {
    try {
      const response = await apiService.getGameTypeConfig(
        {
          appPackageName: APP_LAUNCH.packageName,
          appChannel: APP_LAUNCH.channel,
          appSource: APP_LAUNCH.source,
          appVersion: APP_VERSION,
          // action: '%2Fcommon%2Fapi%2Fset%2Fget'
        },
        this
      );

      if (response.data.code == 200) {
        // 根据配置决定使用真实数据还是调试数据
        // const gameType = enableDebugMode
        //   ? [
        //       {
        //         game_type: "casino",
        //         game_name: "live",
        //         id: "10006",
        //         sort: 10,
        //       },
        //       {
        //         game_type: "slots",
        //         game_name: "spin",
        //         id: "10000",
        //         sort: 11,
        //       },
        //       {
        //         game_type: "poker",
        //         game_name: "cards",
        //         id: "10001",
        //         sort: 4,
        //       },
        //       {
        //         game_type: "SPIN GAMES",
        //         game_name: "arcade",
        //         id: "10000",
        //         sort: 11,
        //       },
        //       {
        //         game_type: "SPIN GAMES",
        //         game_name: "bingo",
        //         id: "10000",
        //         sort: 11,
        //       },
        //       {
        //         game_type: "SPIN GAMES",
        //         game_name: "sports",
        //         id: "10000",
        //         sort: 11,
        //       },
        //     ]
        //   : this.capitalizeGameType(this.sortBySortAsc(response.data.data.game_type));
        const gameType = [
          {
            game_type: "casino",
            game_name: "live",
            id: "10006",
            sort: 10,
          },
          {
            game_type: "slots",
            game_name: "spin",
            id: "10000",
            sort: 11,
          },
          {
            game_type: "poker",
            game_name: "cards",
            id: "10001",
            sort: 4,
          },
          {
            game_type: "SPIN GAMES",
            game_name: "arcade",
            id: "10000",
            sort: 11,
          },
          {
            game_type: "SPIN GAMES",
            game_name: "bingo",
            id: "10000",
            sort: 11,
          },
          {
            game_type: "SPIN GAMES",
            game_name: "sports",
            id: "10000",
            sort: 11,
          },
        ];

        this.setData({
          gameTypeDone: true,
        });

        my.setStorage({
          key: "gameType",
          data: gameType,
          success: () => {
            this.loadingDone();
          },
        });

        // 存储充值最大值，用于连续充值自动判断
        my.setStorage({
          key: "maximum_single_recharge",
          data: response.data.data.maximum_single_recharge,
        });
      } else if (response.data.code == 102121) {
        //系统维护
        this.setData({
          showMessageConfrim: true,
          showMessageTitle: "System Maintenance",
          showMessagePagrm: response.data.msg,
        });
      }
      // IP限制
      else if (response.data.code == 1000051) {
        this.setData({
          ip_restricted: true,
        });
      } else {
        my.showToast({
          type: "fail",
          content: response.data.msg,
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("Failed to get game type config:", error);
      // 注意：apiService 已经处理了大部分错误，这里主要处理网络异常
      // my.showToast({
      //   type: 'fail',
      //   content: 'fail to launch',
      //   duration: 2000,
      // });
    }
  },
  loadingDone() {
    // 根据配置决定是否需要 IP 验证
    const needIpValidation = enableIpValidation;
    const progressReady = this.data.progress >= 99;
    const gameTypeReady = enableDebugMode ? true : this.data.gameTypeDone;
    const ipValidationReady = needIpValidation ? this.data.ipVaildDone : true;

    if (progressReady && gameTypeReady && ipValidationReady) {
      my.redirectTo({
        url: "/pages/game/list",
      });
    }
  },
  onLoad(options) {
    // 根据配置决定是否执行 IP 验证
    if (enableIpValidation) {
      this.validateIp();
    } else {
      // 如果不需要 IP 验证，直接设置为完成状态
      this.setData({
        ipVaildDone: true,
      });
    }

    this.getGameType();
    this.updateProgress();
  },
  onShow(options) {
    if (options) {
      my.alert({
        title: "URL-options",
        content: JSON.stringify(options),
        buttonText: "Ok",
      });
    }
  },
  updateProgress() {
    const interval = setInterval(() => {
      if (this.data.progress >= 100) {
        this.loadingDone();
        clearInterval(interval);
      } else {
        this.setData({
          progress: this.data.progress + 5,
        });
      }
    }, 100); // 每100ms更新一次进度
  },
});
