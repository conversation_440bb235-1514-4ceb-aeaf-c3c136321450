<view a:if="{{currentPopup != null || depositAward}}" class="shadow-box">
  <!-- onTap="onTapOverlay" -->
  <view a:if="{{popupVisible.Tip21Old}}" class="activity-pop messageConfrim activity-active">
    <view class="confrim-title dialog-title-main">
      <view>Responsible Gaming</view>
    </view>
    <!--<view class="dialog-title-top">
      IMPORTANT NOTICE</view> -->
    <view class="dialog-scroll">
      <view class="dialog-title-content">
        <text style="margin-bottom: 20rpx;">
          By clicking "I agree all", you confirm that you are:
        </text>
        <view>
          <text>·</text>
          <text>Over 21 years old.</text>
        </view>
        <view>
          <text>·</text>
          <text>Not a government official.</text>
        </view>
        <view>
          <text>·</text>
          <text>Not a Gaming Employment License holder.</text>
        </view>
        <view>
          <text>·</text>
          <text>Not a member of the Armed Forces of the Philippines, including the Army, Navy, Air Force, or Philippine National Police.</text>
        </view>
        <view>
          <text>·</text>
          <text>Not on PAGCOR's National Database of Restricted Persons.</text>
        </view>
        <view>
          <text>·</text>
          <text>I have read and agree to the
            <text onTap="openTerms" class="dialog-title-content-link">
              Terms of Services
            </text>
          </text>
        </view>
        <!--<text style="margin-top: 20rpx;">Funds or credits on the account of player who is found ineligible to play shall mean forfeiture of said funds/ credits in favor of the Government.</text> -->
      </view>
      <view class="dialog-error-content">
        <image mode="scaleToFill" src="../../assets/new-icons/dialog-warning.png" />
        <view class="dialog-error-content-text">
          Ineligible player funds will be forfeited to the Government.
        </view>
      </view>
    </view>
    <view class="dialog-logos">
      <view
        onTap="onOpenParentLink"
        data-url="https://www.pagcor.ph/regulatory/responsible-gaming.php"
        class="dialog-itembox"
      >
        <image mode="scaleToFill" src="../../assets/new-icons/logo_link_1.png" />
      </view>
      <view class="logo-line"></view>
      <view
        onTap="onOpenParentLink"
        data-url="https://www.pagcor.ph/regulatory/responsible-gaming.php"
        class="dialog-itembox"
      >
        <image style="width: 222rpx;" mode="scaleToFill" src="../../assets/new-icons/logo_link_2.png" />
      </view>
    </view>
    <view class="confirm-flexs">
      <button onTap="onReject21Terms" class="confirm-cancel">Exit</button>
      <view onTap="onConfirmClosePopup" class="confrim-done">I AGREE ALL</view>
    </view>
  </view>
  <!-- 未满21岁禁止访问  -->
  <view class="mask-one flexcenter {{ forbid_access ?'flex':'hiddenno'}}">
    <view class="messageConfrim">
      <view class="confrim-title dialog-title-main">
        <view>Notice</view>
      </view>
      <view class="dialog-title-top small-font">
        You did not agree with our statement, unfortunately we are unable to provide you with services
      </view>
      <view class="confirm-flexs">
        <view onTap="forbidAccessDone" class="confrim-done">DONE</view>
      </view>
    </view>
  </view>
  <!-- 获取VIP提示  -->
  <view a:if="{{popupVisible.VipTip}}" class="activity-pop activity-pop-activity activity-active">
    <view class="vip-tops-logs">
      <image mode="widthFix" style="width: 156px;" src="../../assets/pops/vip-head.png" />
    </view>
    <view class="vip-tops-title">
      Congratulations !
    </view>
    <view class="activity-pop-title">
      <text>You're now a VIP!</text>
    </view>
    <view class="activity-pops-content">
      Enjoy 0.8% rebate and other exclusive privileges!
    </view>
    <view onTap="onConfirmClosePopup" class="activity-pop-confirm-btn">
      <text>VIP Privileges</text>
    </view>
    <view onTap="onConfirmClosePopup" class="activity-pop-close-btn">
      <image mode="scaleToFill" src="../../assets/pops/close.png" />
    </view>
  </view>
  <!-- 通用活动弹窗、调账后奖励、VIP反水-->
  <view
    a:if="{{popupVisible.ActivityBonus || popupVisible.RegisterBonus || popupVisible.BonusPopups}}"
    class="activity-pop-wrap"
  >
    <view class="activity-pop activity-pop-activity activity-active">
      <view class="vip-tops-logs activity-pop-activity-logs">
        <image mode="widthFix" style="width: 176px;" src="../../assets/pops/activity-tips1.png" />
      </view>
      <image mode="widthFix" style="width: 220px;margin-left: 10px;" src="../../assets/pops/Congratulations.svg" />
      <view class="activity-pop-title-VipCashback activity-pop-title-activity">
        You received a bonus in
        <text>{{' '}} {{currentPopup.data.activity_name|| currentPopup.name}}{{' '}}</text> promotion:
      </view>
      <view class="activity-pops-content-VipCashback">
        <text>{{currentPopup.data.amount || currentPopup.data.bonus}}</text>
        <text class="activity-pops-amount-icon">P</text>
      </view>
      <view
        a:if="{{popupVisible.RegisterBonus}}"
        onTap="onConfirmOpenPopup"
        class="activity-pop-confirm-btn activity-pop-confirm-activity"
      >
        <text>Go Play</text>
      </view>
      <view
        a:if="{{popupVisible.ActivityBonus|| popupVisible.BonusPopups}}"
        onTap="onConfirmClaimPopup"
        class="activity-pop-confirm-btn activity-pop-confirm-activity"
      >
        <text>CLAIM</text>
      </view>
      <view
        a:if="{{currentPopup.data.reward_date || currentPopup.data.bonus_date}}"
        class="description-VipCashback"
      >
        <view>Reward Date: {{currentPopup.data.reward_date || currentPopup.data.bonus_date}}</view>
      </view>
      <!--<view
        a:if="{{ !popupVisible.BonusPopups }}"
        onTap="onConfirmClosePopup"
        class="activity-pop-close-btn"
      ><image mode="scaleToFill" src="../../assets/pops/close.png" /></view> -->
    </view>
  </view>

  <!-- 首充奖励-->
  <view a:if="{{depositAward}}" class="activity-pop-wrap">
    <view class="activity-pop activity-pop-activity activity-active">
      <view class="vip-tops-logs activity-pop-activity-logs">
        <image mode="widthFix" style="width: 176px;" src="../../assets/pops/activity-tips1.png" />
      </view>
      <view class="activity-pop-title-VipCashback activity-pop-title-activity">
        You have successfully claimed your bonus.
        Best of luck on your journey ahead!
      </view>
      <view class="activity-pops-content-VipCashback">
        <text>{{depositAward}}</text>
        <text class="activity-pops-amount-icon">P</text>
      </view>
      <view onTap="onConfirmDepositPopup" class="activity-pop-confirm-btn activity-pop-confirm-activity">
        <text>Go Play</text>
      </view>
      <view onTap="closeDepositPopup" class="activity-pop-close-btn">
        <image mode="scaleToFill" src="../../assets/pops/close.png" />
      </view>
    </view>
  </view>

</view>

<!-- 跳转链接弹框 -->
<openlink-dialog
  token="{{token}}"
  jumpTypeInfo="{{jumpTypeInfo}}"
  onClose="offOpenLinkDialog"
  showJumpConfrim_1="{{showJumpConfrim_1}}"
/>