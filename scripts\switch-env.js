/**
 * 环境切换脚本
 * 用于快速切换不同环境的配置
 * 
 * 使用方法：
 * node scripts/switch-env.js dev    # 切换到开发环境
 * node scripts/switch-env.js prod   # 切换到生产环境
 * node scripts/switch-env.js test   # 切换到测试环境
 */

const fs = require('fs');
const path = require('path');

// 支持的环境
const SUPPORTED_ENVIRONMENTS = ['dev', 'prod', 'test'];

// 配置文件路径
const CONFIG_FILE_PATH = path.join(__dirname, '../utils/config.js');

/**
 * 读取配置文件内容
 */
function readConfigFile() {
  try {
    return fs.readFileSync(CONFIG_FILE_PATH, 'utf8');
  } catch (error) {
    console.error('❌ 无法读取配置文件:', error.message);
    process.exit(1);
  }
}

/**
 * 写入配置文件内容
 */
function writeConfigFile(content) {
  try {
    fs.writeFileSync(CONFIG_FILE_PATH, content, 'utf8');
    console.log('✅ 配置文件更新成功');
  } catch (error) {
    console.error('❌ 无法写入配置文件:', error.message);
    process.exit(1);
  }
}

/**
 * 切换环境
 */
function switchEnvironment(targetEnv) {
  if (!SUPPORTED_ENVIRONMENTS.includes(targetEnv)) {
    console.error(`❌ 不支持的环境: ${targetEnv}`);
    console.log(`支持的环境: ${SUPPORTED_ENVIRONMENTS.join(', ')}`);
    process.exit(1);
  }

  console.log(`🔄 正在切换到 ${targetEnv.toUpperCase()} 环境...`);

  // 读取当前配置文件
  let content = readConfigFile();

  // 查找当前环境设置
  const envRegex = /const ENVIRONMENT = ['"`](\w+)['"`];/;
  const match = content.match(envRegex);

  if (!match) {
    console.error('❌ 无法找到 ENVIRONMENT 配置');
    process.exit(1);
  }

  const currentEnv = match[1];
  console.log(`📍 当前环境: ${currentEnv.toUpperCase()}`);

  if (currentEnv === targetEnv) {
    console.log(`✅ 已经是 ${targetEnv.toUpperCase()} 环境，无需切换`);
    return;
  }

  // 替换环境配置
  const newContent = content.replace(envRegex, `const ENVIRONMENT = '${targetEnv}';`);

  // 写入新配置
  writeConfigFile(newContent);

  console.log(`✅ 成功切换到 ${targetEnv.toUpperCase()} 环境`);
  
  // 显示当前配置信息
  showCurrentConfig(targetEnv);
}

/**
 * 显示当前配置信息
 */
function showCurrentConfig(env) {
  console.log('\n📋 当前环境配置:');
  
  // 这里可以根据需要显示具体的配置信息
  const configs = {
    dev: {
      baseUrl: 'https://pre.nustaronline.vip/',
      wwwUrl: 'https://gcash.nustaronline.vip/',
      assetsUrl: 'https://uat-nustar-static.nustaronline.vip/',
    },
    prod: {
      baseUrl: 'https://io.nustargame.com/',
      wwwUrl: 'https://gcash.nustargame.com/',
      assetsUrl: 'https://nustar-static.nustargame.com/',
    },
    test: {
      baseUrl: 'https://test.nustaronline.vip/',
      wwwUrl: 'https://gcash.nustaronline.vip/',
      assetsUrl: 'https://uat-nustar-static.nustaronline.vip/',
    }
  };

  const config = configs[env];
  if (config) {
    Object.entries(config).forEach(([key, value]) => {
      console.log(`  ${key}: ${value}`);
    });
  }
}

/**
 * 显示帮助信息
 */
function showHelp() {
  console.log(`
🔧 环境切换工具

使用方法:
  node scripts/switch-env.js <environment>

支持的环境:
  dev   - 开发环境（预发）
  prod  - 生产环境
  test  - 测试环境

示例:
  node scripts/switch-env.js dev     # 切换到开发环境
  node scripts/switch-env.js prod    # 切换到生产环境
  node scripts/switch-env.js test    # 切换到测试环境
`);
}

// 主程序
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    showHelp();
    process.exit(1);
  }

  const command = args[0].toLowerCase();
  
  if (command === 'help' || command === '-h' || command === '--help') {
    showHelp();
    return;
  }

  switchEnvironment(command);
}

// 运行主程序
if (require.main === module) {
  main();
}

module.exports = {
  switchEnvironment,
  showCurrentConfig,
  SUPPORTED_ENVIRONMENTS
};
