page {
  height: auto;
  background: #f3f1f4;
  z-index: -2;
}

/* @font-face {
  font-family: 'DIN-Medium';
  src: url('../../assets/DIN-Medium.otf') format('opentype');
}
:root {
  --theme-color: #BA1245;
} */
.container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  height: calc(100vh - 300rpx);
  overflow-y: scroll;
}

.swiper-container {
  background: #fff;
}

.swiper-content {
  width: calc(100% - 64rpx);
  margin: 0 auto;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.swiper-content image {
  width: 100%;
  /* 宽高保持比例 343/ 94 */
  aspect-ratio: 343 / 94;
}

.page-list {
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(to bottom, #fff 10%, #f4f8f8 100%);
}

.flex-tops {
  width: 100%;
  padding-bottom: 10rpx;
  background: #fff;
}

.windowtitle {
  display: flex;
  align-items: center;
  justify-content: left;
}

.windowtitle-vip {
  /* background: rgba(255, 219, 158, 1); */
}

.windowtitle>image {
  background-size: contain;
  width: 167rpx;
  height: 80.5rpx;
  /* padding-left: 44rpx; */
  margin-left: 44rpx
}

.user-box {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 16rpx 32rpx;
}

.flex-wrap {
  flex-wrap: wrap;
  gap: 20rpx;
}

.user-box-top {
  padding-top: 30rpx;
  justify-content: space-between;
}

.wallet-call {
  display: flex;
  flex-direction: column;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  color: #FFF;
  width: calc(100% - 64rpx);
  margin: 0 auto;
  /* 宽高保持比例 343/ 153 */
  aspect-ratio: 343 / 153;
  border-radius: 20px;
  padding-bottom: 20px;
}

.isvip.wallet-call {
  background-color: rgba(255, 249, 234, 1);
}

.novip.wallet-call {
  background-color: rgba(241, 242, 246, 1);

}

.wallet-top {
  display: flex;
  align-items: flex-start;
  align-items: center;
  height: 48px;
  padding: 0 16px;
  justify-content: space-between;
  border-radius: 20px 20px 0 0;
}

.isvip .wallet-top {
  background: linear-gradient(to right, rgba(105, 60, 0, 1) 20%, rgba(26, 13, 0, 1) 100%);
}

.novip .wallet-top {
  background: linear-gradient(to right, rgba(85, 85, 85, 1) 20%, rgba(26, 13, 0, 1) 100%);
}



.transaction-records {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.transaction-records>text {
  font-size: 24rpx;
  color: #FFF;
}

.wallet-content {
  /* display: flex; */
  /* align-items: baseline; */
  padding: 0 24rpx;
  /* font-size: 78rpx; */
  font-weight: 500;
  line-height: 140%;
  margin: 20rpx 0;
  font-family: 'DIN-Medium';
  color: rgba(34, 34, 34, 1);
  font-size: 28px;
}

.wallet-content .balance {
  display: flex;
  align-items: center;
  gap: 10px;
}

.amount-icon {
  color: #AC1140;
}

.balance-refresh {
  width: 17px;
  height: 17px;
  margin: 6px 4px 0;
  background-image: url('../../assets/new-icons/refrush.png');
  background-size: contain;
  background-repeat: no-repeat;
}

.wallet-content>image {
  margin-left: 20rpx;
}

.wallet-content .wallet-header {
  display: flex;
  width: 100%;
  flex-grow: 1;
  justify-content: space-between;
  font-size: 14px;
  color: rgba(153, 153, 153, 1);
}

.wallet-content .wallet-header>text:last-child {
  color: rgba(34, 34, 34, 1);
}

.wallet-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx;
  gap: 34rpx;
}

.wallet-bottom>view {
  flex: 1;
}

.wallet-bottom image {
  vertical-align: sub;
}

.wallet-bottom .withdraw,
.wallet-bottom .topup {
  width: 153px;
  height: 48px;
  border-radius: 999px;
  background: #fff;
  text-align: center;
  color: rgba(156, 68, 0, 1);
  line-height: 48px;
}

.wallet-bottom view.topup {
  background-color: rgba(172, 17, 64, 1);
  color: #fff;
}

.wallet-bottom>view>image {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}



.notification {
  display: flex;
  align-items: center;
  background: rgba(244, 247, 253, 1);
  border-radius: 32rpx;
  box-sizing: border-box;
  padding: 16rpx 24rpx;
  width: calc(100% - 48rpx);
  margin: 16rpx auto;
  background-color: rgba(0, 0, 0, 0.05);
  margin-top: 0;
}

.notification .icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.notif-container {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
}

.notif-message {
  display: inline-block;
  white-space: nowrap;
  animation: marquee var(--marquee-duration) linear infinite;
  padding-left: 100%;
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

/* 当文本悬停时暂停动画 */
.notif-message:hover {
  animation-play-state: paused;
}

.justspace {
  display: flex;
  justify-content: space-between;
}

.shadow {
  background: rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  padding: 16rpx 30rpx;
  border-radius: 40rpx/50%;
}

.margin-bottom {
  margin-bottom: 10rpx;
}

.user-set-box {
  flex: 1;
  display: flex;
  align-items: center;
  margin-left: auto;
  margin-bottom: 10rpx;
  white-space: nowrap;
}

.user-set-box>view {
  flex: 1;
  text-align: center;
}

.user-id {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.user-id-copy {
  margin-left: 10rpx;
  width: 36rpx;
  height: 36rpx;
}

.flexline {
  display: flex;
  align-items: center;
}

.wallet-box {
  flex: 1;
}

.wallet-box>text {
  margin-right: 12rpx;
}

.wallet-box>image {
  margin-left: auto;
}

.circ-btn {
  background: #252D3F;
  border-radius: 40rpx/50%;
  padding: 16rpx 30rpx;
  color: rgba(254, 206, 127, 1);
  margin-left: 10rpx;
}

.balance-box-bg {
  width: 100%;
  background-repeat: no-repeat;
  background-image: url('/assets/home/<USER>');
  background-size: 100% 100%;
}

.idtext-and-transaction {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  padding-left: 46rpx;
}

.idtext {
  font-size: 30rpx;
  color: #ffd8ae;
  display: flex;
  align-items: center;
  align-self: flex-start;
  margin-top: 10rpx;
}

.idtext text {
  margin-left: 10rpx;

}

.transaction-box-button image {
  width: 307rpx;
  height: 70rpx;
  margin-right: -6rpx;
}

.balance-info {
  height: 80rpx;
  display: flex;
  justify-content: left;
  align-items: center;
  padding-left: 100rpx;
}

.balance-info text {
  margin-left: 20rpx;
  font-size: 60rpx;
  font-weight: bold;
  color: #ffffff;
  margin-top: 18rpx;
}

.balance-box {
  width: 100%;
  height: 120rpx;
  display: flex;
  justify-content: space-around;
  align-items: center;
  margin-bottom: 20rpx;
}

.balance-box .redeem-box-button {
  margin-left: 20rpx;
}

.playlist-bg {
  width: 100%;
  height: 260rpx;
}

.box-button-common {
  float: left;
  height: 68rpx;
  text-align: left;
  color: #010000;
  font-size: 28rpx;
  margin-left: 40rpx;
}

.box-button-common1 {
  float: left;
  height: 68rpx;
  text-align: left;
  color: #010000;
  font-size: 28rpx;
}

.deposit-icon {
  width: 307rpx;
  height: 88rpx;
  margin-bottom: -5rpx;
  margin-right: 20rpx;
}

.close-popwindow-icon {
  width: 100rpx;
  height: 100rpx;
}

.gamelist-box {
  width: 100%;
  padding-bottom: 100px;
}

.gamelist {
  display: flex;
  width: 94%;
  margin: 0 auto;
  gap: 2%;
  flex-wrap: wrap;
}

.gamestypelist {
  width: calc(100% - 64rpx);
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  gap: 2%
}

.gamestypelist>image {
  width: 49%;
  aspect-ratio: 166 / 116;
  margin-bottom: 16rpx;
}

.regulatory-logo {
  /* width: calc(100% - 64rpx); */
  box-sizing: border-box;
  padding: 32rpx;
  background: #FFF;
  border-radius: 20rpx;
  margin: 16rpx 32rpx 0;

  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 10rpx;
}

.regulatory-logo>image {
  width: 30%;
}

.regulatory-logo>view {
  color: #eee;
}

.regulatory-logo>image:first-child {
  width: 16%;
}

.hotgamelist {
  display: block;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;
  flex-shrink: 1;
  padding-bottom: 190rpx;
  margin: 0 20rpx;
  float: left;
  width: 100%;
}

.gamelist-icon {
  width: 30rpx;
  height: 35rpx;
}

.gamelist-item {
  width: 49%;
  height: 242rpx;
  box-sizing: border-box;
  /* padding: 10rpx 10rpx 10rpx 10rpx; */
  margin-top: 16rpx;
  float: left;
  position: relative;
  border-radius: 20rpx;
  overflow: hidden;
}

.gamelist-item-image {
  width: 100%;
  height: 100%;
}

.cate-title {
  position: absolute;
  top: 10%;
  left: 6%;
  padding: 8rpx 16rpx;
  border-radius: 20%/50%;
  color: rgba(255, 202, 113, 1);
  background: rgba(254, 206, 127, 0.15);

}

.gamelist-list-item-name {
  position: relative;
  z-index: 99;
  text-align: center;
  font-weight: bold;
  font-size: 28rpx;
  margin: 5rpx 0;
  color: #333333;
}

.hotgamelist-item {
  width: 24%;
  height: 175rpx;
  box-sizing: border-box;
  padding: 10rpx 10rpx 10rpx 10rpx;
  margin-bottom: 40rpx;
  float: left;
}

.hotgamelist-item-image {
  width: 96%;
  height: 96%;
}

.hotgamelist-list-item-name {
  position: relative;
  text-align: center;
  font-size: 24rpx;
  margin: 5rpx 0;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 35rpx;
}

.gamelist-title {
  margin: 40rpx 30rpx 32rpx;
}

.gamelist-title text {
  color: #010000;
  font-size: 35rpx;
  font-weight: bold;
}

.help-dialog-title {
  font-size: 36rpx;
}

.help-dialog-content {
  color: #999999;
  margin: 30rpx 0;
  font-size: 28rpx;
}

.f-btn-primary {
  background: #7454c3;
  margin: 30rpx 140rpx 0 140rpx;
  border-radius: 30rpx;
}

.mask {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  right: 0;
  background: #00000090;
  /* opacity: 0; */
  display: none;
  z-index: 2;
}


.mask-layer-4.mask {
  z-index: 4;
}

.mask-one {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  background: #00000090;
  display: none;
  z-index: 3;
}

.flex {
  display: flex;
}

.flexcenter {
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 101;
}

.flex-zindex {
  z-index: 9999;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
}

.hiddenno {
  display: none;
}

.messageConfrim {
  z-index: 4;
  background: #FFF;
  border-radius: 30rpx;
  width: calc(100% - 60rpx);
  box-sizing: border-box;
  padding: 32rpx;
  margin-bottom: 100rpx;
}

.messageConfrim_ipset {
  padding: 60rpx 44rpx;
}

.confrim-title {
  color: #000;
  font-size: 44rpx;
  font-weight: 500;
  text-align: center;
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
}

.confrim-title>image {
  width: 48rpx;
  height: 48rpx;
  /* margin-left: auto; */
}

.dialog-title-main {
  text-align: center;
  color: #000;
  font-size: 48rpx;
  font-weight: 500;
  margin-top: 0;
  justify-content: center;
}

.dialog-title-top {
  text-align: center;
  font-size: 24rpx;
  color: #000;
  margin: 32rpx 0 0;
}

.dialog-scroll {
  /* height: 650rpx; */
  /* overflow-y: auto; */
  margin-top: 20rpx;
}

.small-font {
  font-size: 36rpx;
}

.email-text {
  text-align: left;
  margin-top: 20rpx;
  color: #AC1140;
}

.ipforbid {
  padding: 20rpx;
}

.confrim-param {
  text-align: center;
  font-size: 30rpx;
  color: #707070;
  margin: 32rpx;
  line-height: 44rpx;
}

.dialog-title-content {
  text-align: left;
  font-size: 24rpx;
  font-weight: 400;
  color: #4F6477;
  line-height: 170%;
  border-radius: 8px;
  background: #F4F4F4;
  padding: 20rpx;
}

.dialog-title-content>view {
  display: flex;
}

.dialog-title-content>view>text:first-child {
  margin-right: 10rpx;
}

.dialog-title-content-link {
  margin-left: 6rpx;
  color: var(--theme-color);
}

.dialog-error-content {
  display: flex;
  justify-content: space-between;
  margin-top: 32rpx;
  background-color: #FFEFB9;
  padding: 20rpx;
  border-radius: 10rpx;
}

.dialog-error-content>image {
  width: 40rpx;
  height: 40rpx;
}

.dialog-error-content-text {
  width: calc(100% - 60rpx);
  font-size: 24rpx;
  color: #6C5711;
  font-size: 24rpx;
  line-height: 170%;
}

.dialog-logos {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 32rpx;
}

.dialog-logos image {
  height: 64rpx;
  width: 260rpx;
}

.logo-line {
  height: 64rpx;
  width: 2rpx;
  background: #DDD;
}

.confirm-flexs {
  display: flex;
  justify-content: space-between;
  gap: 30rpx;

}

.confrim-done {
  flex: 1;
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  font-weight: 500;
  background-color: var(--theme-color);
  color: #FFF;
  font-size: 36rpx;
  text-align: center;
  border-radius: 48px;
  margin-top: 48rpx;
}

.confirm-cancel {
  flex: 1;
  background: #FFF;
  color: #000;
  border: 2rpx solid #ddd;
  height: 96rpx;
  line-height: 96rpx;
  font-weight: 500;
  font-size: 36rpx;
  text-align: center;
  border-radius: 48px;
  margin-top: 52rpx;
}

.activity-pop-content {
  width: 100%;
  position: fixed;
  top: 160rpx;
  z-index: 3;
  text-align: center;
}

.deposit-box {
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
  z-index: 2;
  overflow: hidden;
  display: none;
  background-color: #FFF;
  border-radius: 40rpx 40rpx 0 0;
  overflow: hidden;
  /* background-image: url('../../assets/buycoinbg.png');
  background-repeat: no-repeat;
  background-position: center top; */
  /* background-size: 100%; */
}

.deposit-box-title {
  text-align: center;
}

.deposit-box-title image {
  width: 54%;
  height: 42px;
}

.redeem-box {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;
  overflow: hidden;
  display: none;
  background-image: url('../../assets/redeembg.png');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: 100%;
}

.redeem-box-title {
  text-align: center;
}

.redeem-box-title image {
  width: 54%;
  height: 42px;
}

.transaction-box {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;
  overflow: hidden;
  display: none;
  border-radius: 40rpx 40rpx 0 0;
  width: 100%;
  background: #FFF;
  /* background-image: url('../../assets/transactionbg.png');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: 100%;
  width: 100%; */
}

.transaction-box-title {
  text-align: center;
  padding: 50rpx 32rpx 30rpx;
  font-size: 42rpx;
  font-weight: 600;
}

.transaction-box-title image {
  width: 54%;
  height: 42px;
}

.detail-title {
  display: flex;
  justify-content: space-between;
  /* justify-content: left; */
  text-align: center;
  align-items: center;
}

.detail-title-center {
  flex: 1;
}

.detail-title image {
  width: 40rpx;
  height: 40rpx;
  /* margin-right: 16rpx; */
}

.detail-title-close {
  /* margin-left: auto; */
  float: right;
}

.detail-title-close>image {
  width: 48rpx;
  height: 48rpx;
}

.userinfo-box-title {
  padding: 50rpx 20rpx 30rpx;
  font-weight: 600;
  color: #000;
}

.userinfo-content {
  background-color: #FFF;
  box-sizing: border-box;
  padding: 32rpx;
  padding-top: 0;
}

.user-info-item {
  /* display: flex;
  justify-content: center; */
}

.user-info-item>view {
  font-size: 28rpx;
  color: rgba(80, 80, 80, 1);
  font-weight: 400;
  margin: 30rpx 0 20rpx;
}

.user-info-item>input,
.user-id-input {
  height: 84rpx;
  line-height: 84rpx;
  border: 2rpx solid #E4E4E4;
  /* width: calc(100% - 64rpx); */
  width: 95%;
  border-radius: 100rpx;
  text-indent: 5%;
  padding-left: 20rpx;
  border-radius: 100px;
  border: 1px solid #E4E4E4;
  background: #f4f7fd;
}

.user-id-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-id-input>image {
  margin-right: 40rpx;
}

.user-id-input>input {
  width: 80%;
  background: #f4f7fd;
}

.user-inputs {
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 80rpx;
}

.user-inputs>input {
  line-height: 84rpx;
  height: 84rpx;
  text-indent: 5%;
  border: 2rpx solid #E4E4E4;
  border-radius: 100rpx;

  border-radius: 100px;
  border: 1px solid #E4E4E4;
  background: rgba(244, 247, 253, 1);
  overflow: hidden;
}


.wallet-main {
  box-sizing: border-box;
  padding: 32rpx;
  padding-bottom: 0;
}

.wallet-balance {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  color: #999999;
  font-size: 14px;
}

.wallet-balance>image {
  width: 20px;
  height: 20px;
}

.clienterror {
  color: rgb(255, 72, 72);
  width: 100%;
  text-align: center;
}

.rule-message {
  color: rgba(144, 144, 144, 1);
  font-size: 28rpx;
  display: flex;
  width: 100%;
}

.amout-line {
  display: flex;
  align-items: center;
  position: relative;
}

.amout-line image {
  width: 30rpx;
  height: 30rpx;
  margin-left: 6rpx;
}

.message-box {
  /* position: absolute; */
  /* top: 40rpx; */
  /* left: 60rpx; */
  color: #909090;
  box-sizing: border-box;
  padding: 16rpx;
  /* width: 50vw; */
  background-color: #FCFCFC;
  border-radius: 8rpx;
  /* z-index: 9; */
}

.shadowbox {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  margin: auto;
  /* background: rgba(0, 0, 0, 0.05); */
  background: transparent;
}

.price-input {
  height: 48px;
  border-radius: 999px;
  border: 2rpx solid #E4E4E4;
  display: flex;
  align-items: center;
  margin-top: 40rpx;
  font-family: 'DIN-Medium';
  background-color: rgba(244, 247, 253, 1);
}

.red-input {
  border: 2rpx solid rgba(255, 72, 72, 1);
}

.price-input>input {
  margin-left: 20rpx;
  width: 85%;
  font-weight: 400;
  font-size: 36rpx;
  color: rgba(172, 17, 64, 1);
  background-color: transparent;
}

.price-input>input::placeholder {
  font-size: 26rpx;
}

.price-input>view {
  margin-left: auto;
  margin-right: 5%;

  font-size: 56rpx;
  font-weight: 500;
}

.price-selection {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
  margin-top: 32rpx;
  font-family: 'DIN-Medium';
  /* max-height: 160rpx;
  overflow-y: scroll; */
}

.messagecontent {}

.price-select-item {
  line-height: 40px;
  height: 40px;
  text-align: center;
  color: rgba(102, 102, 102, 1);
  background: rgba(244, 247, 253, 1);
  border-radius: 999px;
  border: 2rpx solid transparent;
  width: calc((100% - 56rpx) / 3);
  font-size: 18px;
  position: relative;
}

.price-select-item-active {
  border: 2rpx solid rgba(172, 17, 64, 1);
  background-color: rgba(172, 17, 64, 0.05);
  color: rgba(172, 17, 64, 1);
}

.price-bonus {
  display: flex;
  align-items: baseline;
  margin-top: 28rpx;
  gap: 40rpx;
}

.price-bonus>view {
  width: 45%;
  display: flex;
  align-items: baseline;
}

.price-bonus>view>text {
  color: #6C6C6C;
  font-size: 28rpx;
  margin-right: 24rpx;
}

.price-bonus>view>view {
  color: #000;
  font-size: 48rpx;
  font-weight: 500;
  font-family: 'DIN-Medium';
}

.price-bonus>view>.glod {
  color: var(--theme-color);
}

.first-recharge-award {
  position: absolute;
  left: -5px;
  top: -5px;
  color: #FFF;
  height: 22px;
  width: 54px;
  line-height: 20px;
  text-align: left;
  padding: 0 8rpx;
  font-size: 22rpx;
  background-image: url('../../assets/new-icons/first-recharge-award.svg');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  box-sizing: border-box;
}



.price-continue {
  margin: 60rpx auto 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90%;
  height: 96rpx;
  background: var(--theme-color);
  opacity: 0.5;
  color: #FFF;
  border-radius: 100rpx;
}

.before-checkbox-btn {
  margin: 60rpx auto 10rpx !important;
}

.topUpBatches-checkbox-group {
  padding: 0 0 80rpx;
  text-align: center;
  color: #999999;
}

.checkbox-round {
  border-radius: 50%;
}

.checkbox-round-active {
  background-color: #AC1140;
}

.active-continue {
  opacity: 1;
}

.transaction-filter-all {
  float: left;
  width: 100%;
  padding: 30rpx;
}

.transaction-filter {
  float: left;
  padding: 10rpx 0;
}

.picker-list {
  display: flex;
  background: #f5f5f5;
  border-radius: 999px;
  width: 90%;
  margin: 20rpx auto;
  margin-top: 40rpx;
}

.picker-list>view {
  width: 33%;
  line-height: 74rpx;
  text-align: center;
  border-radius: 999px;
  background: #F5F5F5;
  color: #000;
}

.picker-list>.activePickClass {
  background-color: var(--theme-color);
  color: #FFF;
}

.time-selects {
  flex-wrap: wrap;
  gap: 20rpx;
  background: none;
  margin-top: 30rpx;
}

.time-selects>view {
  width: auto;
  border: 2rpx solid #ccc;
  background: #FFF;
  /* width: calc( (100% - 72rpx) / 4); */
  padding: 0 40rpx;
}

.time-selects>.picker-red {
  color: var(--theme-color);
  line-height: 56rpx;
  padding: 0 28rpx;
  border-radius: 60rpx;
}

.picker-list>.activeTimeClass {
  background: #AC1140;
  border: 2rpx transparent;
  color: #FFF;
}

.transaction-filter .row {
  background-image: url('../../assets/transaction-filter-bg.png');
  background-size: 204rpx 80rpx;
  background-repeat: no-repeat;
  height: 80rpx;
  width: 204rpx;
  font-size: 24rpx;
  padding-left: 15rpx;
  padding-top: 5rpx;
  font-weight: bold;
}

.transaction-filter .row-title {
  color: #cccccc;
}

.transaction-filter .row-extra {
  color: #333333;
}

.transaction-select-list-area {
  text-align: center;
  /* padding-bottom: 130%; */
  background-color: #FFF;
  padding-bottom: 50rpx;
  min-height: 600rpx;
  max-height: 700rpx;
  /* overflow: scroll; */
  width: 100%;
}

.transaction-select-list-area scroll-view {
  height: 500rpx;
  width: 100%;
}

.history-times {
  background: #FFF;
}

.history-title-time {
  text-align: left;
  box-sizing: border-box;
  padding: 30rpx;
  border-bottom: 2rpx solid #EEE;
}

.history-item {
  width: 94%;
  /* border: #e1eaf1 1px solid; */
  background-color: white;
  border-radius: 20rpx;
  box-sizing: border-box;
  padding: 22rpx;
  margin: 10rpx 20rpx;
  display: flex;
}

.history-quantity {
  position: absolute;
  bottom: 0;
  right: 0;
  border-radius: 12px;
  background: #666;
  padding: 0px 6px;
  font-size: 14px;
  color: #fff;
}

/* 充值 */
/* status successful */
.status-success .status-left-color {
  color: #11BE6B;
}

.status-success>.status-right-color {
  color: #000;
}

.status-success .detail-type-name {
  color: #11BE6B;
  background: rgb(223, 255, 239);
  border: 2rpx solid transparent;
}


/* status waiting */
/* waiting */
.status-pending .status-left-color {
  color: #227BFF;
}

.status-pending>.status-right-color {
  color: #000;
}

.status-pending .detail-type-name {
  color: #227BFF;
  background: #DCEAFF;
  border: 2rpx solid transparent;
}

/* waiting change balance */
.status-waitingpay .status-left-color {
  color: #FFB701;
}

.status-waitingpay>.status-right-color {
  color: #000;
}

.status-waitingpay .detail-type-name {
  color: #FFB701;
  border: 2rpx solid transparent;
  background: rgba(255, 183, 1, 0.3);
}

/* sttus failure */
/* failure */
.status-failure .status-left-color {
  color: #FF4848;
}

.status-failure>.status-right-color {
  color: #000;
}

.status-failure .detail-type-name {
  color: #FF4848;
  background: #FFE3E3;
  border: 2rpx solid transparent;
}


/* status withdraw */
.item_withdraw_1 .status-left-color {
  color: #227BFF;
}

.item_withdraw_4 .status-left-color {
  color: #227BFF;
}

.item_withdraw_5 .status-left-color {
  color: #227BFF;
}

.item_withdraw_2 .status-left-color {
  color: #11BE6B;
}

.item_withdraw_2>.status-right-color {
  color: #000;
}

.item_withdraw_3 .status-left-color {
  color: #FF4848;
}

.item_withdraw_3 .status-right-color {
  color: #000;
}

/* 分批提现 */
.mergeOrder-detailList-head {
  text-align: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.mergeOrder-detailList-head-title {
  line-height: 36px;
}

.mergeOrder-detailList-head-amount {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 20px;
}

.mergeOrder-detailList-head-no {
  color: #999999;
}


.history-icon {
  position: relative;
  /* width: 88rpx;
  height: 88rpx; */
  border-radius: 50%;
  background: #F5F5F5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 16rpx;
}

.history-icon>image {
  width: 100rpx;
  height: 100rpx;
}

.history-icon>.gift-icon {
  width: 60rpx;
  height: 60rpx;
}

.whtid-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FBBC05;
  color: #FFF;
  font-size: 32rpx;
  font-weight: 500;
  margin-right: 16rpx;
}

.reward-icon {
  color: #909090;
  background-color: #EEE;
}

.item-left {
  max-width: 65%;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  text-align: left;
}

.item-right {
  text-align: right;
  padding-right: 10rpx;
  margin-top: 32rpx;
  margin-left: auto;
  color: #909090;
  font-weight: 500;
  font-size: 32rpx;
}

.more-button {
  background-color: #fff;
  margin: 0 34rpx;
  margin-top: 24rpx;
  margin-bottom: 0.24rem;
  text-align: center;
  color: #555555;
  height: 90rpx;
  border-radius: 45rpx;
  line-height: 90rpx;
}

.no-record {
  color: #999999;
}

.deposit-confirm-box {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  z-index: 4;
  background-color: #fff;
  border-top-right-radius: 50rpx;
  border-top-left-radius: 50rpx;
  text-align: center;
  overflow: hidden;
  padding: 40rpx 0;
  padding-top: 0;
  display: none;
}

.deposit-confirm-content {
  color: #92796b;
  font-size: 28rpx;
  font-weight: bold;
  padding: 30rpx 20rpx 20rpx 30rpx;
}

.deposit-confirm-button-image {
  width: 96%;
  height: 120rpx;
}

.deposit-confirm-title {
  padding: 30rpx 0 0 0;
  background-color: #ffffff;
}

.deposit-confim-main {
  box-sizing: border-box;
  padding-bottom: 0;
}

.deposit-confim-main>view {
  width: calc(100% - 80rpx);
  margin: 0 auto;
}

.confirm-main-margintop {
  margin-top: 40rpx;
}

.detail-main {}

.detail-main-title {
  display: flex;
  font-size: 34rpx;
  font-weight: 500;
  justify-content: center;
  align-items: center;
}

.logo-background {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #F5F5F5;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 10rpx;
}

.logo-background image {
  width: 60rpx;
  height: 60rpx;
}

.detail-banlance {
  font-size: 60rpx;
  font-weight: 500;
  margin-top: 34rpx;
}

.banlance-up {
  color: #11BE6B;
}

.banlance-down {
  color: #FF4848;
}

.detail-type {
  margin-top: 32rpx;
}

.detail-type>view {
  padding: 8rpx 24rpx;
  border-radius: 84rpx;
  width: fit-content;
  margin: 0 auto;
}

.detail-type-name {
  border: 2rpx solid #bbb;
}

.success {
  color: #11BE6B;
  background: rgb(223, 255, 239);
  border: 2rpx solid transparent;
}

.pending {
  color: #227BFF;
  background: #DCEAFF;
  border: 2rpx solid transparent;

}

.waiting {
  color: #FF9900;
  border: 2rpx solid transparent;
  background: #FFEECE;
}

.failure {
  color: #FF4848;
  background: #FFE3E3;
  border: 2rpx solid transparent;
}

.detail-lists {
  margin-top: 48rpx;
  margin-bottom: 100rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  color: #909090;
  font-size: 28rpx;
  padding: 16rpx 0;
}

.detail-item>image {
  width: 40rpx;
  height: 40rpx;
  margin-left: 6rpx;
}

.detail-item .detail-item-right {
  font-size: 30rpx;
  font-weight: 500;
  color: #303030;
  margin-left: auto;
  text-align: right;
  /* max-width: 60%; */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.detail-line {
  width: 100%;
  height: 2rpx;
  background-color: #EEE;
}

.radio-group {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  font-weight: 500;
  /* padding: 12rpx 40rpx; */
}

.radio-lable {
  display: flex;
  justify-content: space-between;
  margin-top: 32rpx;
}

.deposit-confim-main>.confim-value {
  color: #000;
  font-weight: 500;
  font-size: 60rpx;
  padding: 0 32rpx 20rpx;
  margin-bottom: 40rpx;
  font-family: 'DIN-Medium';
}

/* .deposit-confim-main>.confim-line {
  width: 100%;
  height: 2rpx;
  background-color: rgba(0, 0, 0, 0.09);
  margin: 32rpx 0;
} */

.times-statue-select {
  padding: 0 32rpx;
}

.font-din-medium {
  font-family: 'DIN-Medium';
}

.confim-item {
  font-size: 34rpx;
  color: #909090;
  padding: 28rpx 10rpx;
}

.boldfont {
  color: #000;
  font-weight: 500;
}

.deposit-award {
  color: var(--theme-color);
}

.deposit-confirm-bottom {
  padding: 50rpx 0;
  background-color: #f4f4f4;
}

.deposit-confirm-title-t {
  color: #7a7a7a;
  font-size: 28rpx;
  margin-left: 50rpx;
}

.deposit-confirm-title-coins {
  color: #ff7d14;
  font-size: 45rpx;
  margin-left: 15rpx;
}

.deposit-select-box {
  padding: 0 30rpx;
  background-color: #fff;
  min-height: 700rpx;
}

.deposit-select-title {
  border-bottom: 1px solid #cccccc;
  line-height: 80rpx;
  color: #666666;
}

.deposit-confirm-title-line {
  margin-top: -15rpx;
}

.deposit-confirm-title-line image {
  width: 100%;
  height: 1px;
}

.deposit-select-list {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: start;
  flex-shrink: 1;
  padding-top: 15rpx;
}

.deposit-list-item {
  position: relative;
  width: 33%;
  height: 220rpx;
  box-sizing: border-box;
  color: #898989;
  text-align: center;
  font-size: 20rpx;
  padding: 10rpx;
  margin-bottom: 14rpx;
}

.list-item-image {
  width: 100%;
  height: 100%;
}

.deposit-list-item-coins {
  position: relative;
  z-index: 99;
  text-align: center;
  font-weight: bold;
  font-size: 24rpx;
  color: #90351d;
  bottom: 100rpx;
}

.deposit-list-item-extra-coins {
  position: absolute;
  z-index: 99;
  text-align: center;
  font-size: 24rpx;
  font-weight: bold;
  color: #F20702;
  left: 4rpx;
  top: 8rpx;

  background-image: url('/assets/free.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center;
}

.deposit-list-item-extra-coins-text {
  transform: rotate(-3deg);
  margin: 10rpx 20rpx;
}


.deposit-list-item-amount {
  position: relative;
  z-index: 99;
  text-align: center;
  font-size: 28rpx;
  color: #ffffff;
  bottom: 80rpx;
  font-weight: bold;
}

/* .deposit-list-item-amount-with-extra {
bottom: 100rpx;
}
.deposit-list-item-amount-without-extra {
bottom: 75rpx;
} */
.redeem-list-item {
  width: 33%;
  height: 240rpx;
  box-sizing: border-box;
  color: #898989;
  text-align: center;
  font-size: 20rpx;
  padding: 10rpx;
  margin-bottom: 14rpx;
}

.redeem-list-item-name {
  position: relative;
  text-align: center;
  font-size: 22rpx;
  line-height: 20rpx;
  color: #955449;
  bottom: 100rpx;
}

.redeem-list-item-image {
  position: relative;
  width: 100rpx;
  height: 100rpx;
  bottom: 180rpx;
}

.redeem-list-item-bgimage {
  position: relative;
  width: 100%;
  height: 100%;
}

.redeem-list-item-exchange {
  position: relative;
  z-index: 99;
  text-align: center;
  font-size: 28rpx;
  color: #ffffff;
  bottom: 75rpx;
  font-weight: bold;
}

.deposit-confirm-button {
  text-align: center;
  margin: 60rpx 0 60rpx 0;
}

.not-scroll {
  height: 100vh;
  overflow: auto;
}

.no-more-data {
  height: 100rpx;
  width: 100%;
}

.explore-games-button {
  width: 100%;
  text-align: center;
  position: fixed;
  bottom: 0%;
  /* background-image: url('../../assets/explore-games-bg.png');
  background-size: 100% 100%; */
  /* background: #FFF; */
  z-index: 1;
}

.explore-games-button-image {
  width: 90%;
  height: 120rpx;
  margin: 30rpx 0;
}

.service-game {
  position: fixed;
  right: 5%;
  bottom: 15%;
  width: 94rpx;
  height: 94rpx;
  border-radius: 50%;
}

.service-game>image {
  width: 100%;
  height: 100%;
}

.allgame-btn {
  width: 90%;
  margin: 30rpx auto 60rpx;
  line-height: 96rpx;
  background-color: #BA1245;
  color: #FFF;
  text-align: center;
  font-size: 16px;
  font-weight: 500;
  /* border-radius: 40rpx/50%; */
  border-radius: 100rpx;
}

.image-pts {
  width: 46rpx;
  /* height: 40rpx; */
  margin-bottom: -10rpx;
}

.image-id {
  width: 28rpx;
  height: 28rpx;
}

.image-refresh-balance {
  width: 40rpx;
  height: 40rpx;
  margin: 0 0 -8rpx 30rpx;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.rotating-element {
  animation: rotate 20s linear infinite;
}

.show {
  display: block;
}

.hidden {
  display: none;
}

.focusInput {
  max-height: 40vh;
  overflow-y: scroll;
}

:root {
  --marquee-duration: 100s;
  /* 默认值 */
}

.notif-container {
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
  color: #000;
}

.notif-message {
  display: inline-block;
  white-space: nowrap;
  animation: marquee var(--marquee-duration) linear infinite;
  padding-left: 100%;
}

.status-picker {
  background: rgba(245, 245, 245, 1) !important;
  border: none !important;
}

@keyframes marquee {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(-100%);
  }
}

.notif-message:hover {
  animation-play-state: paused;
}

/* 下载引导浮标样式 */
.download-guide-float {
  position: fixed;
  z-index: 2;
  width: 120rpx;
  height: 120rpx;
}

/* 浮标位置 */
.download-guide-float.bottom-right {
  bottom: 330rpx;
  right: 18rpx;
}

/* 浮标图标 */
.download-guide-icon {
  width: 52px;
  height: 52px;
  border-radius: 50%;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

/* 提示气泡 */
.download-guide-tooltip {
  position: absolute;
  bottom: 130rpx;
  right: 0;
  min-width: 240rpx;
  max-width: 360rpx;
  padding: 20rpx 24rpx;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  font-size: 24rpx;
  line-height: 1.4;
  border-radius: 16rpx;
  text-align: center;
  word-wrap: break-word;
  animation: tooltip-fade-in 0.3s ease-out;
}

/* 气泡箭头 */
.download-guide-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  right: 20rpx;
  width: 0;
  height: 0;
  border-left: 12rpx solid transparent;
  border-right: 12rpx solid transparent;
  border-top: 12rpx solid rgba(0, 0, 0, 0.8);
}

/* 浮标动画已移除 */

/* 提示气泡淡入动画 */
@keyframes tooltip-fade-in {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 点击效果 */
.download-guide-float:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}