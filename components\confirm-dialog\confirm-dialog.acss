.messageConfrim {
  z-index: 4;
  background: #FFF;
  border-radius: 30rpx;
  width: calc(100% - 60rpx);
  box-sizing: border-box;
  padding: 32rpx;
  margin-bottom: 400rpx;
}
.confrim-title {
  color: #000;
  font-size: 44rpx;
  font-weight: 500;
  text-align: center;
  margin-top: 20rpx;
}
.confrim-param {
  text-align: center;
  font-size: 30rpx;
  color: #707070;
  margin: 32rpx;
  line-height: 44rpx;
}
.confrim-done {
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  background: #202635;
  color: #FECE7F;
  font-weight: 500;
  font-size: 36rpx;
  text-align: center;
  border-radius: 48px;
  margin-top: 52rpx;
}

.mask {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  right: 0;
  background: #00000090;
  /* opacity: 0; */
  display: none;
  z-index: 2;
}

.mask-one {
  position: fixed;
  width: 100%;
  height: 100%;
  left: 0;
  right: 0;
  background: #00000090;
  /* opacity: 0; */
  display: none;
  z-index: 3;
}
.flex {
  display: flex;
}
.flexcenter {
  display: flex;
  justify-content: center;
  align-items: center;
}
.hiddenno {
  display: none;
}
.messageConfrim {
  z-index: 4;
  background: #FFF;
  border-radius: 30rpx;
  width: calc(100% - 60rpx);
  box-sizing: border-box;
  padding: 32rpx;
  margin-bottom: 400rpx;
}