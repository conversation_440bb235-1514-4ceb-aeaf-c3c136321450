#!/usr/bin/env node

/**
 * JavaScript 兼容性检查脚本
 * 检查项目中可能不兼容的 JavaScript 语法
 * 使用方法: node scripts/check-compatibility.js
 */

const fs = require("fs");
const path = require("path");

// 需要检查的文件扩展名
const FILE_EXTENSIONS = [".js"];

// 需要排除的目录和文件
const EXCLUDE_PATTERNS = ["node_modules", ".history", ".git", "dist", "build", "scripts/check-compatibility.js", "scripts/fix-optional-chaining.js"];

/**
 * 兼容性检查规则
 */
const COMPATIBILITY_RULES = [
  {
    name: "可选链操作符 (?.)",
    pattern: /\?\./g,
    description: "可选链操作符在某些环境中不被支持",
    suggestion: "使用 obj && obj.prop 替代 obj?.prop",
  },
  {
    name: "空值合并操作符 (??)",
    pattern: /\?\?(?!=)/g, // 排除 ??= 的情况
    description: "空值合并操作符在某些环境中不被支持",
    suggestion: "使用 value !== null && value !== undefined ? value : defaultValue",
  },
  {
    name: "逻辑赋值操作符 (||=, &&=, ??=)",
    pattern: /(\|\|=|&&=|\?\?=)/g,
    description: "逻辑赋值操作符在某些环境中不被支持",
    suggestion: "使用传统的赋值方式",
  },
];

/**
 * 检查文件是否应该被处理
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  if (!FILE_EXTENSIONS.includes(ext)) {
    return false;
  }

  const relativePath = path.relative(process.cwd(), filePath).replace(/\\/g, "/");

  for (const pattern of EXCLUDE_PATTERNS) {
    if (relativePath.includes(pattern)) {
      return false;
    }
  }

  return true;
}

/**
 * 递归获取所有需要处理的文件
 */
function getAllFiles(dir) {
  const files = [];

  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);

      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory()) {
          const relativePath = path.relative(process.cwd(), fullPath);
          if (!EXCLUDE_PATTERNS.some((pattern) => relativePath.includes(pattern))) {
            traverse(fullPath);
          }
        } else if (stat.isFile() && shouldProcessFile(fullPath)) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`警告: 无法读取目录 ${currentDir}: ${error.message}`);
    }
  }

  traverse(dir);
  return files;
}

/**
 * 检查文件的兼容性问题
 */
function checkFileCompatibility(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    const issues = [];

    for (const rule of COMPATIBILITY_RULES) {
      const matches = content.match(rule.pattern);
      if (matches) {
        // 获取匹配的行号
        const lines = content.split("\n");
        const matchedLines = [];

        lines.forEach((line, index) => {
          if (rule.pattern.test(line)) {
            matchedLines.push({
              lineNumber: index + 1,
              content: line.trim(),
            });
          }
        });

        issues.push({
          rule: rule.name,
          description: rule.description,
          suggestion: rule.suggestion,
          count: matches.length,
          lines: matchedLines,
        });
      }
    }

    return issues;
  } catch (error) {
    console.warn(`警告: 无法读取文件 ${filePath}: ${error.message}`);
    return [];
  }
}

/**
 * 主函数
 */
function main() {
  console.log("🔍 检查 JavaScript 兼容性问题...\n");

  const files = getAllFiles(process.cwd());
  let totalFiles = 0;
  let filesWithIssues = 0;
  let totalIssues = 0;

  const allIssues = {};

  for (const filePath of files) {
    totalFiles++;
    const issues = checkFileCompatibility(filePath);

    if (issues.length > 0) {
      filesWithIssues++;
      const relativePath = path.relative(process.cwd(), filePath);
      allIssues[relativePath] = issues;

      issues.forEach((issue) => {
        totalIssues += issue.count;
      });
    }
  }

  // 输出结果
  if (filesWithIssues === 0) {
    console.log("✅ 恭喜！没有发现兼容性问题。");
  } else {
    console.log(`⚠️  发现 ${totalIssues} 个兼容性问题，涉及 ${filesWithIssues} 个文件:\n`);

    for (const [filePath, issues] of Object.entries(allIssues)) {
      console.log(`📄 ${filePath}:`);

      issues.forEach((issue) => {
        console.log(`   ❌ ${issue.rule} (${issue.count} 处)`);
        console.log(`      ${issue.description}`);
        console.log(`      建议: ${issue.suggestion}`);

        if (issue.lines.length <= 3) {
          issue.lines.forEach((line) => {
            console.log(`      第 ${line.lineNumber} 行: ${line.content}`);
          });
        } else {
          console.log(`      出现在第 ${issue.lines.map((l) => l.lineNumber).join(", ")} 行`);
        }
        console.log("");
      });
    }
  }

  console.log(`\n📊 检查统计:`);
  console.log(`   总文件数: ${totalFiles}`);
  console.log(`   有问题的文件: ${filesWithIssues}`);
  console.log(`   总问题数: ${totalIssues}`);

  if (filesWithIssues > 0) {
    console.log("\n💡 修复建议:");
    console.log("1. 优先修复可选链操作符 (?.) 和空值合并操作符 (??)");
    console.log("2. 使用传统的条件检查替代新语法");
    console.log("3. 修复后运行测试确保功能正常");
    console.log("4. 可以使用 git diff 查看具体更改");
  }

  // 返回退出码
  process.exit(filesWithIssues > 0 ? 1 : 0);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkFileCompatibility,
  COMPATIBILITY_RULES,
};
