/**
 * API 端点配置
 * 统一管理所有 API 接口地址和相关配置
 */

// ============================================================================
// 用户相关接口
// ============================================================================
export const USER_API = {
  // 登录相关
  LOGIN: {
    url: "api/player/login",
    baseUrl: "common",
    method: "POST",
  },

  // 用户信息
  GET_INFO: {
    url: "api/player/info",
    baseUrl: "common",
    method: "POST",
  },

  // 用户 KYC 信息
  GET_KYC: {
    url: "api/get/user/kyc",
    baseUrl: "common",
    method: "POST",
    description: "获取用户KYC认证信息",
    timeout: 8000, // 8秒超时
    retryable: true, // 支持重试
  },

  // 获取跳转参数
  GET_JUMP_PARAMS: {
    url: "api/gcash/get/jumpType",
    baseUrl: "common",
    method: "POST",
  },
};

// ============================================================================
// 支付相关接口
// ============================================================================
export const PAYMENT_API = {
  // 充值
  BALANCE_ADD: {
    url: "api/payment/balance-add",
    baseUrl: "common",
    method: "POST",
  },

  // 充值通知
  RECHARGE_NOTIFY: {
    url: "api/pay-service/recharge-notify",
    baseUrl: "common",
    method: "POST",
  },

  // 提现
  EXCHANGE: {
    url: "api/exchange",
    baseUrl: "common",
    method: "POST",
  },

  // 提现风控检查
  WITHDRAW_RISK: {
    url: "api/withdraw/risk",
    baseUrl: "common",
    method: "POST",
  },
};

// ============================================================================
// 配置相关接口
// ============================================================================
export const CONFIG_API = {
  // 充值提现配置
  RECHARGE_WITHDRAW_CONFIG: {
    url: "api/global-config/recharge-withdraw",
    baseUrl: "common",
    method: "POST",
  },

  // 首充规则配置
  FIRST_RECHARGE_RULE: {
    url: "api/global-config/first/recharge/rule",
    baseUrl: "common",
    method: "POST",
  },

  // 下载引导配置
  DOWNLOAD_GUIDE_CONFIG: {
    url: "open/api/base/guide",
    baseUrl: "main",
    method: "POST",
  },

  // 游戏类型配置
  GAME_TYPE_CONFIG: {
    url: "api/set/get",
    baseUrl: "common",
    method: "POST",
  },
};

// ============================================================================
// 活动相关接口
// ============================================================================
export const ACTIVITY_API = {
  // 活动枚举配置
  ADJUSTMENT_ENUM: {
    url: "api/activity/adjustment/enum",
    baseUrl: "avt",
    method: "GET",
  },

  // 横幅活动列表
  BANNER_LIST: {
    url: "api/banner/activity/list",
    baseUrl: "avt",
    method: "POST",
  },
};

// ============================================================================
// 记录相关接口
// ============================================================================
export const RECORD_API = {
  // 充值记录
  RECHARGE_RECORD: {
    url: "api/get/recharge/record",
    baseUrl: "common",
    method: "GET",
  },

  // 奖励记录
  AWARD_RECORD: {
    url: "api/get/award",
    baseUrl: "common",
    method: "GET",
  },

  // 提现记录列表
  WITHDRAWAL_LIST: {
    url: "api/exchange/list",
    baseUrl: "common",
    method: "GET",
  },

  // 提现合并订单详情
  WITHDRAWAL_MERGE_ORDERS: {
    url: "api/exchange",
    baseUrl: "avt",
    method: "GET",
  },
};

// ============================================================================
// 游戏相关接口
// ============================================================================
export const GAME_API = {
  // 返回大厅 / 获取用户余额
  BACK_TO_HALL: {
    url: "api/jili/backhall",
    baseUrl: "common",
    method: "POST",
    description: "返回游戏大厅并获取用户当前余额",
    timeout: 10000, // 10秒超时
    retryable: true, // 支持重试
  },
};

// ============================================================================
// 系统相关接口
// ============================================================================
export const SYSTEM_API = {
  // 跑马灯消息
  MARQUEE_LIST: {
    url: "api/marquee/list",
    baseUrl: "common",
    method: "GET",
  },
};

// ============================================================================
// 辅助函数
// ============================================================================

/**
 * 根据 API 配置构建请求选项
 * @param {Object} apiConfig - API 配置对象
 * @param {Object} data - 请求数据
 * @param {Object} options - 额外选项
 * @returns {Object} 请求选项
 */
export function buildRequestOptions(apiConfig, data = {}, options = {}) {
  const requestOptions = {
    url: apiConfig.url,
    baseUrl: apiConfig.baseUrl,
    method: apiConfig.method,
    data,
    ...options,
  };

  // 如果 API 配置中有超时设置，添加到请求选项中
  if (apiConfig.timeout) {
    requestOptions.timeout = apiConfig.timeout;
  }

  // 如果 API 配置中有重试设置，添加到请求选项中
  if (apiConfig.retryable !== undefined) {
    requestOptions.retryable = apiConfig.retryable;
  }

  // 如果 API 配置中有描述，添加到请求选项中用于调试
  if (apiConfig.description) {
    requestOptions.description = apiConfig.description;
  }

  return requestOptions;
}

/**
 * 构建带 ID 的 API URL
 * @param {Object} apiConfig - API 配置对象
 * @param {string|number} id - ID 参数
 * @returns {Object} 更新后的 API 配置
 */
export function buildApiWithId(apiConfig, id) {
  return {
    ...apiConfig,
    url: `${apiConfig.url}/${id}`,
  };
}

/**
 * 获取所有 API 端点的扁平化列表
 * @returns {Object} 所有 API 端点
 */
export function getAllApiEndpoints() {
  return {
    ...USER_API,
    ...PAYMENT_API,
    ...CONFIG_API,
    ...ACTIVITY_API,
    ...RECORD_API,
    ...GAME_API,
    ...SYSTEM_API,
  };
}

// ============================================================================
// 常用 API 组合
// ============================================================================

/**
 * 用户初始化需要的 API 列表
 */
export const USER_INIT_APIS = [USER_API.GET_INFO, USER_API.GET_KYC, CONFIG_API.RECHARGE_WITHDRAW_CONFIG, CONFIG_API.DOWNLOAD_GUIDE_CONFIG, ACTIVITY_API.ADJUSTMENT_ENUM];

/**
 * 交易记录相关 API 映射
 */
export const TRANSACTION_API_MAP = {
  0: RECORD_API.RECHARGE_RECORD, // 充值记录
  1: RECORD_API.WITHDRAWAL_LIST, // 提现记录
  2: RECORD_API.AWARD_RECORD, // 奖励记录
};

// ============================================================================
// 导出所有 API 配置
// ============================================================================
export default {
  USER_API,
  PAYMENT_API,
  CONFIG_API,
  ACTIVITY_API,
  RECORD_API,
  GAME_API,
  SYSTEM_API,
  buildRequestOptions,
  buildApiWithId,
  getAllApiEndpoints,
  USER_INIT_APIS,
  TRANSACTION_API_MAP,
};
