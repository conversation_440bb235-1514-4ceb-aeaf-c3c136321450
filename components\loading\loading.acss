/* loading-progress/loading-progress.wxss */
.progress-ring {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.ring-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4rpx solid #f0f0f0;
  border-radius: 50%;
  box-sizing: border-box;
}

.ring-progress {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  overflow: hidden;
}

.progress-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4rpx solid #ff0000;
  border-radius: 50%;
  box-sizing: border-box;
}

.mask {
  position: absolute;
  top: 0;
  width: 50%;
  height: 100%;
  overflow: hidden;
}

.left {
  left: 0;
}

.right {
  right: 0;
}

.mask-inner {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background: #fff;
  transform-origin: 100% 50%;
  transition: transform 0.2s ease;
}

.left .mask-inner {
  left: 100%;
  transform: rotate(-180deg);
}

.right .mask-inner {
  right: 100%;
  transform: rotate(-180deg);
}

.center {
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  right: 4rpx;
  bottom: 4rpx;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.center text {
  font-size: 28rpx;
  font-weight: bold;
}