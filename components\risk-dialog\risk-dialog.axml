<view class="risk-dialog-mask {{visible ? 'show' : ''}}">
</view>
<view class="risk-dialog {{visible ? 'show' : ''}}">
  <image class="risk-dialog-toplogo" mode="widthFix" src="../../assets/risk-dialog/risk-top.png" />
  <view class="risk-dialog-title">
    Before Withdrawal
  </view>

  <view class="risk-dialog-content">
    <view class="risk-tips">
      You need to complete
      <text class="risk-tips-text">{{needBet}} P</text>Wager turnover
    </view>
    <view class="progress-container">
      <text class="progress-label">Valid Wager</text>
      <view class="progress-bar">
        <view class="progress-inner" style="width: {{progressWidth}}"></view>
        <view class="progress-text">{{showCurrentBet}}/{{showTotalBet}}</view>
      </view>
    </view>
  </view>

  <view class="risk-dialog-footer">
    <view class="btn-go-bet" onTap="handleConfirm">Go Play</view>
  </view>

  <view class="close-btn" onTap="handleCancel">
    <image src="../../assets/risk-dialog/risk-close.png" />
  </view>
</view>