.shadow-box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.messageConfrim {
  z-index: 4;
  background: #FFF;
  border-radius: 30rpx;
  width: calc(100% - 60rpx);
  box-sizing: border-box;
  padding: 32rpx;
  margin-bottom: 100rpx;
  height: auto;
}

.confrim-title {
  color: #000;
  font-size: 44rpx;
  font-weight: 500;
  text-align: center;
  margin-top: 20rpx;
  display: flex;
  justify-content: space-between;
}

.confrim-title>image {
  width: 48rpx;
  height: 48rpx;
}

.dialog-title-main {
  text-align: center;
  color: #000;
  font-size: 48rpx;
  font-weight: 500;
  margin-top: 0;
  justify-content: center;
}

.dialog-scroll {
  margin-top: 20rpx;
}

.dialog-title-content {
  text-align: left;
  font-size: 24rpx;
  font-weight: 400;
  color: #4F6477;
  line-height: 170%;
  border-radius: 8px;
  background: #F4F4F4;
  padding: 20rpx;
}

.dialog-title-content>view {
  display: flex;
}

.dialog-title-content>view>text:first-child {
  margin-right: 10rpx;
}

.dialog-title-content-link {
  margin-left: 6rpx;
  color: var(--theme-color);
}

.dialog-error-content {
  display: flex;
  justify-content: space-between;
  margin-top: 32rpx;
  background-color: #FFEFB9;
  padding: 20rpx;
  border-radius: 10rpx;
}

.dialog-error-content>image {
  width: 40rpx;
  height: 40rpx;
}

.dialog-error-content-text {
  width: calc(100% - 60rpx);
  font-size: 24rpx;
  color: #6C5711;
  font-size: 24rpx;
  line-height: 170%;
}

.dialog-logos {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 32rpx;
}

.dialog-logos image {
  height: 64rpx;
  width: 260rpx;
}

.logo-line {
  height: 64rpx;
  width: 2rpx;
  background: #DDD;
}

.confirm-flexs {
  display: flex;
  justify-content: space-between;
  gap: 30rpx;

}

.confrim-done {
  flex: 1;
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  font-weight: 500;
  background-color: var(--theme-color);
  color: #FFF;
  font-size: 36rpx;
  text-align: center;
  border-radius: 48px;
  margin-top: 48rpx;
}

.confirm-cancel {
  flex: 1;
  background: #FFF;
  color: #000;
  border: 2rpx solid #ddd;
  height: 96rpx;
  line-height: 96rpx;
  font-weight: 500;
  font-size: 36rpx;
  text-align: center;
  border-radius: 48px;
  margin-top: 52rpx;
}

.small-font {
  font-size: 36rpx;
}

.activity-pop {
  width: calc(100% - 90rpx);
  background-color: #fff;
  border-radius: 72rpx;
  box-sizing: border-box;
  padding: 40rpx;

  transform: scale(0.2);
  transition: all 0.3s ease;
  pointer-events: none;
}

.activity-active {
  transform: scale(1);
  transition: all 0.3s ease;
  pointer-events: auto;
}

.activity-pop-wrap {
  background-image: url('../../assets/pops/light.png');
  text-align: center;
  height: 600px;
  background-size: cover;
  width: 100%;
  background-position: top -30px left 0;
}

.activity-pop-activity {
  background: linear-gradient(to bottom, rgba(255, 246, 216, 1) 0%, rgba(255, 255, 255, 1) 60%);
  box-shadow: 0px 1.7px 1px 0px rgba(255, 255, 255, 0.80) inset;
  position: relative;
  margin: 170px auto;
  padding-top: 116rpx;
}

.activity-pop-title {
  font-size: 42rpx;
  font-weight: 500;
  color: rgba(216, 144, 0, 1);
  text-align: center;
  margin-top: 38rpx;
}

.vip-tops-logs {
  position: absolute;
  top: -192rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 100vw;
  text-align: center;
}

.vip-tops-title {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 52px;
  line-height: 52px;
  background-position: left 50% top 0;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  background-image: url('../../assets/pops/vip-headbg.svg');
  background-repeat: no-repeat;
}

.vip-tops-logs>image {
  width: 878rpx;
  text-align: center;
  margin-left: -64rpx;
}

.activity-pop-activity-logs {
  top: -1.4rem;
  left: 56%;
}

.activity-pops-content {
  font-size: 32rpx;
  font-weight: 400;
  color: #38394F;
  text-align: center;
  line-height: 174%;
  margin-top: 20rpx;
  margin-bottom: 0.8rem;
}

.activity-pop-confirm-btn {
  font-size: 36rpx;
  font-weight: 500;
  color: #fff;
  text-align: center;
  background: rgba(34, 34, 34, 1);
  border-radius: 96rpx;
  line-height: 96rpx;
}

.activity-pop-confirm-activity {
  background-color: transparent;
  color: #FFF;
  background-image: url('../../assets/pops/btnbg.svg');
  height: 56px;
  background-repeat: no-repeat;
  background-position: center;
  line-height: 56px;
}

.bonus-popups-date {
  margin-bottom: 20rpx;
}

.activity-pop-close-btn {
  position: absolute;
  bottom: -120rpx;
  left: 50%;
  transform: translateX(-50%);
}

.activity-pop-close-btn>image {
  width: 60rpx;
  height: 60rpx;
}

.activity-pop-title-VipCashback {
  font-size: 32rpx;
  font-weight: 500;
  color: #38394F;
  text-align: left;
  margin-top: 20rpx;
}

.activity-pop-title-VipCashback>text {
  color: #ff5100;
  margin-left: 10rpx;
  margin-right: 10rpx;
  display: inline-block;
  width: auto;
}

.activity-pop-title-activity {
  margin-top: 60rpx;
  padding: 0 16px;
}

.activity-pop-title-activity>text {
  display: contents;
}

.activity-pops-content-VipCashback {
  box-sizing: border-box;
  padding: 36rpx 32rpx;
  border-radius: 16rpx;
  text-align: center;
  position: relative;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.activity-pops-content-VipCashback>view {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F8AA00;
  width: 66rpx;
  height: 66rpx;
  border-radius: 50%;
  color: #FFF;
  font-weight: bold;
  font-size: 48rpx;
  margin-right: 20rpx;
}

.activity-pops-amount-icon {
  color: #FF5101 !important;
}

.activity-pops-content-VipCashback>text {
  font-size: 48rpx;
  font-weight: 700;
  color: #28293A;
}

.description-VipCashback {
  font-size: 24rpx;
  font-weight: 400;
  color: #A0A1BD;
  text-align: center;
}

.description-VipCashback>view {
  margin-top: 20rpx;
}