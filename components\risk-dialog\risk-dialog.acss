.risk-dialog-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
}

.risk-dialog {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.8);
  width: 654rpx;
  border-radius: 20rpx;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s;
  background: linear-gradient(359deg, #FFF 39.98%, #FFF7DD 96.1%), #FFF;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.10);
  box-sizing: border-box;
  padding: 40rpx;
  padding-top: 130rpx;
}

.risk-dialog-toplogo {
  position: absolute;
  top: -130rpx;
  width: calc(297rpx / 327rpx * 100%);
  margin: 0 auto;
}
.risk-dialog.show,
.risk-dialog-mask.show {
  opacity: 1;
  visibility: visible;
}

.risk-dialog.show {
  transform: translate(-50%, -50%) scale(1);
}

.risk-dialog-title {
  position: relative;
  padding: 30rpx;
  text-align: center;
  font-size: 36rpx;
  font-weight: 500;
  border-bottom: 1px solid #eee;
  padding-top: 0;
}

.close-icon {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 32rpx;
  height: 32rpx;
}

.risk-dialog-content {
  background: #F4F4F4;
  padding: 32rpx 24rpx;
  border-radius: 20rpx;
}

.risk-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-container {
  display: flex;
  align-items: center;
  margin-top: 26rpx;
}

.progress-label {
  font-size: 26rpx;
  color: #465664;
  margin-right: 30rpx;
  white-space: nowrap;
}

.progress-bar {
  flex: 1;
  height: 40rpx;
  background: #001405;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
}

.progress-inner {
  height: 100%;
  background: #0EB100;
  border-radius: 20rpx;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 24rpx;
  z-index: 1;
  white-space: nowrap;
}

.bet-values {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  padding: 0 10rpx;
}

.current-bet {
  color: #0EB100;
  font-size: 28rpx;
  font-weight: 500;
}

.total-bet {
  color: #666;
  font-size: 28rpx;
}

.risk-tips {
  font-size: 26rpx;
  color: #000;
  text-align: left;
  border-radius: 20rpx;
}

.risk-tips-text {
  color: #0EB100;
  font-size: 28rpx;
  font-weight: 500;
  margin-left: 10rpx;
  margin-right: 10rpx;
}

.risk-dialog-footer {
  margin-top: 48rpx;
}

.btn-go-bet {
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  color: #fff;
  font-size: 32rpx;
  border-radius: 200rpx;
  background: linear-gradient(90deg, #EC2032 0%, #FF9431 100%), 
              linear-gradient(0deg, #BA1245 0%, #BA1245 100%), 
              linear-gradient(90deg, #F15F26 0%, #FFB800 107.66%);
}

.close-btn {
  position: absolute;
  left: 50%;
  bottom: -120rpx;
  transform: translateX(-50%);
  width: 60rpx;
  height: 60rpx;
  padding: 20rpx;
}

.close-btn image {
  width: 100%;
  height: 100%;
}
