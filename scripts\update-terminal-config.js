#!/usr/bin/env node

/**
 * 批量更新 terminal: 8 为 terminal: APP_DEVICE.terminal 的脚本
 * 使用方法: node scripts/update-terminal-config.js
 */

const fs = require('fs');
const path = require('path');

// 需要更新的文件列表
const filesToUpdate = [
  'pages/game/list.js',
  'utils/http.js'
];

// 替换规则
const replacements = [
  {
    from: /terminal:\s*8,/g,
    to: 'terminal: APP_DEVICE.terminal,'
  },
  {
    from: /terminal:\s*8\s*}/g,
    to: 'terminal: APP_DEVICE.terminal }'
  }
];

/**
 * 更新单个文件
 */
function updateFile(filePath) {
  const fullPath = path.join(process.cwd(), filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`❌ 文件不存在: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(fullPath, 'utf8');
  let hasChanges = false;
  let changeCount = 0;

  // 应用所有替换规则
  replacements.forEach(rule => {
    const matches = content.match(rule.from);
    if (matches) {
      content = content.replace(rule.from, rule.to);
      changeCount += matches.length;
      hasChanges = true;
    }
  });

  if (hasChanges) {
    // 检查是否已经导入了 APP_DEVICE
    if (!content.includes('APP_DEVICE')) {
      console.log(`⚠️  ${filePath} 需要手动添加 APP_DEVICE 导入`);
      console.log('   请在导入语句中添加: APP_DEVICE');
    }

    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ 已更新 ${filePath} - 替换了 ${changeCount} 处`);
    return true;
  } else {
    console.log(`ℹ️  ${filePath} - 无需更新`);
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始批量更新 terminal 配置...\n');

  let totalUpdated = 0;
  let totalFiles = 0;

  filesToUpdate.forEach(filePath => {
    totalFiles++;
    if (updateFile(filePath)) {
      totalUpdated++;
    }
  });

  console.log(`\n📊 更新完成:`);
  console.log(`   总文件数: ${totalFiles}`);
  console.log(`   已更新: ${totalUpdated}`);
  console.log(`   无需更新: ${totalFiles - totalUpdated}`);

  if (totalUpdated > 0) {
    console.log('\n⚠️  注意事项:');
    console.log('1. 请检查更新后的文件是否正确导入了 APP_DEVICE');
    console.log('2. 建议运行测试确保功能正常');
    console.log('3. 可以使用 git diff 查看具体更改');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { updateFile, replacements };
