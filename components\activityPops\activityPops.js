import PopupManager from "./PopupManager";

/**
 * 活动弹窗组件
 * 负责管理和显示各种活动弹窗，包括年龄验证、VIP提示、注册奖励等
 */
Component({
  mixins: [],
  data: {
    // 当前展示的弹窗信息
    currentPopup: null,

    // 弹窗类型映射表，控制不同类型弹窗的显示/隐藏
    popupVisible: {
      Tip21Old: false,
      VipTip: false,
      VipCashback: false,
      ActivityBonus: false,
      RegisterBonus: false,
      BonusPopups: false,
    },

    // 重置可见性状态（备用）
    resetVisible: {
      Tip21Old: false,
      VipTip: false,
      VipCashback: false,
      ActivityBonus: false,
      RegisterBonus: false,
      BonusPopups: false,
    },

    // 未满21岁禁止访问弹框
    forbid_access: false,

    // 活动数据（保留兼容性）
    activityPops: [],

    // 跳转链接弹框
    showJumpConfrim_1: false,
    jumpTypeInfo: {
      type: "game",
      id: "123",
    },

    // 首充奖励金额
    depositAward: "",
  },

  props: {
    token: "",
    // 首充奖励 特殊判断
    deposit_award: "",
    info: {
      token: "",
      isVip: false,
      register_award: {},
      is_register: 0,
    },
    // 首冲奖励关闭回调
    onCloseDepositPopup: Function,
    // 关闭调账奖励弹窗后回调更新余额
    onUpdateAvailableCoins: Function,
  },

  /**
   * 组件挂载时初始化弹窗管理器
   */
  didMount() {
    console.log("ActivityPops didMount:", this.props.info);

    this.initializePopupManager();

    this.setData({
      token: (this.props.info && this.props.info.token) || "",
      depositAward: this.props.deposit_award || "",
    });
  },

  /**
   * 组件更新时处理props变化
   */
  didUpdate(prevProps) {
    console.log("ActivityPops didUpdate:", this.props.info);

    // 检查是否需要重新初始化弹窗管理器
    const shouldReinitialize = this.shouldReinitializeManager(prevProps);

    if (shouldReinitialize) {
      this.initializePopupManager();
    }

    // 更新首充奖励
    if (prevProps.deposit_award !== this.props.deposit_award) {
      this.setData({
        depositAward: this.props.deposit_award || "",
      });
    }

    // 更新token
    this.setData({
      token: (this.props.info && this.props.info.token) || "",
    });
  },

  methods: {
    /**
     * 初始化弹窗管理器
     */
    initializePopupManager() {
      if (this.props.info && this.props.info.token) {
        try {
          this.popupManager = new PopupManager(this.props.info);
          this.popupManager.setPopupChangeCallback(this.handlePopupChange.bind(this));
          this.popupManager.initialize();
        } catch (error) {
          console.error("Failed to initialize PopupManager:", error);
        }
      }
    },

    /**
     * 检查是否需要重新初始化弹窗管理器
     */
    shouldReinitializeManager(prevProps) {
      const currentInfo = this.props.info || {};
      const prevInfo = prevProps.info || {};

      return (prevInfo.token !== currentInfo.token && currentInfo.token) || prevInfo.awardTypes !== currentInfo.awardTypes;
    },

    /**
     * 初始化弹窗可见性
     */
    initPopupVisible() {
      const resetVisible = {};
      for (let key in this.data.popupVisible) {
        resetVisible[key] = false;
      }
      this.setData({
        popupVisible: resetVisible,
      });
    },
    // 打开外部链接（监管条款）
    onOpenParentLink(e) {
      // 打开外部链接（监管条款）
      const url = e.currentTarget.dataset.url;
      this.onConfirmOpenLinkPopup({
        data: {
          type: "parent",
          id: "123",
          url: url,
        },
      });
    },

    // 确认首充奖励弹窗，打开跳转
    onConfirmDepositPopup() {
      // 首冲奖励跳转
      this.onConfirmOpenLinkPopup({
        data: {
          type: "Bet",
          id: "123",
        },
      });
      // 关闭首充奖励弹窗
      this.closeDepositPopup();
    },
    // 关闭首充奖励弹窗
    closeDepositPopup() {
      this.props.onCloseDepositPopup();
    },
    /**
     * 处理弹窗变化
     * @param {Object|null} popup - 弹窗对象，null表示关闭所有弹窗
     */
    handlePopupChange(popup) {
      console.log("Popup change:", popup);

      // 重置所有弹窗可见性
      const popupVisible = {
        Tip21Old: false,
        VipTip: false,
        VipCashback: false,
        ActivityBonus: false,
        RegisterBonus: false,
        BonusPopups: false,
      };

      // 设置当前弹窗可见
      if (popup && popup.id) {
        popupVisible[popup.id] = true;
      }

      // 更新数据
      this.setData({
        popupVisible: popupVisible,
        currentPopup: popup,
      });

      // 当所有弹窗关闭时，延迟更新余额
      if (!popup && typeof this.props.onUpdateAvailableCoins === "function") {
        setTimeout(() => {
          this.props.onUpdateAvailableCoins();
        }, 1000);
      }
    },

    // 拒绝条款（21岁验证）
    onReject21Terms() {
      // this.popupManager.closeCurrentPopup({ agree: false });
      this.openForbidAccess();
    },

    /**
     * 确认关闭按钮
     */
    onConfirmClosePopup() {
      if (this.popupManager) {
        this.popupManager.closeCurrentPopup({
          type: "close",
        });
      }
    },

    /**
     * 确认跳转按钮 - 前往游戏
     */
    onConfirmOpenPopup() {
      if (this.data.currentPopup) {
        this.onConfirmOpenLinkPopup(this.data.currentPopup);
      }
    },

    /**
     * 确认领取按钮
     */
    onConfirmClaimPopup() {
      if (this.popupManager) {
        this.popupManager.closeCurrentPopup({
          type: "claim",
        });
      }
    },

    /**
     * 处理跳转按钮
     * @param {Object} popup - 弹窗对象
     */
    onConfirmOpenLinkPopup(popup) {
      if (!popup || !popup.data) {
        console.error("Invalid popup data for link navigation");
        return;
      }

      this.setData({
        showJumpConfrim_1: true,
        jumpTypeInfo: {
          type: popup.data.type || "game",
          id: popup.data.id || "123",
        },
      });

      if (this.popupManager) {
        this.popupManager.closeCurrentPopup({
          type: "close",
        });
      }
    },
    // 关闭跳转链接弹框
    offOpenLinkDialog() {
      this.setData({
        showJumpConfrim_1: false,
      });
    },
    // 关闭弹窗（只适用于允许关闭的弹窗）
    onClosePopup() {
      return;
      const currentPopup = this.data.currentPopup;
      if (currentPopup && currentPopup.allowClose) {
        this.popupManager.closeCurrentPopup({
          closed: true,
        });
      }
    },

    // 打开未满21岁禁止访问弹框
    openForbidAccess() {
      // this.initPopupVisible();
      this.setData({
        forbid_access: true,
      });
    },

    // 未满21岁禁止访问弹框关闭
    forbidAccessDone() {
      this.setData({
        forbid_access: false,
      });
    },

    // 点击弹窗外部区域
    onTapOverlay() {
      const currentPopup = this.data.currentPopup;
      if (currentPopup && currentPopup.allowClose) {
        this.onClosePopup();
      }
    },

    // 打开条款弹窗
    openTerms() {
      my.navigateTo({
        url: "/pages/terms/terms",
      });
    },
  },
});
