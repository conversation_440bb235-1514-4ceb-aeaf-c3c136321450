import PopupManager from "./PopupManager";

/**
 * 活动弹窗组件 - 管理和显示各种活动弹窗
 */
Component({
  data: {
    // 当前展示的弹窗信息
    currentPopup: null,

    // 弹窗类型映射表，控制不同类型弹窗的显示/隐藏
    popupVisible: {
      Tip21Old: false,
      VipTip: false,
      VipCashback: false,
      ActivityBonus: false,
      RegisterBonus: false,
      BonusPopups: false,
    },

    // 未满21岁禁止访问弹框
    forbid_access: false,
    // 跳转链接弹框
    showJumpConfrim_1: false,
    jumpTypeInfo: { type: "game", id: "123" },
    // 首充奖励金额
    depositAward: "",
    token: "",
  },

  props: {
    deposit_award: "",
    info: {
      token: "",
      isVip: false,
      register_award: {},
      is_register: 0,
    },
    // 首冲奖励关闭回调
    onCloseDepositPopup: Function,
    // 关闭调账奖励弹窗后回调更新余额
    onUpdateAvailableCoins: Function,
  },

  /**
   * 组件挂载时初始化弹窗管理器
   */
  didMount() {
    this.initializePopupManager();
    this.setData({
      token: (this.props.info && this.props.info.token) || "",
      depositAward: this.props.deposit_award || "",
    });
  },

  /**
   * 组件更新时处理props变化
   */
  didUpdate(prevProps) {
    const currentInfo = this.props.info || {};
    const prevInfo = prevProps.info || {};

    // 重新初始化条件：token变化或awardTypes变化
    if ((prevInfo.token !== currentInfo.token && currentInfo.token) || prevInfo.awardTypes !== currentInfo.awardTypes) {
      this.initializePopupManager();
    }

    // 更新数据
    this.setData({
      token: currentInfo.token || "",
      depositAward: this.props.deposit_award || "",
    });
  },

  methods: {
    /**
     * 初始化弹窗管理器
     */
    initializePopupManager() {
      if (!this.props.info || !this.props.info.token) return;

      try {
        this.popupManager = new PopupManager(this.props.info);
        this.popupManager.setPopupChangeCallback(this.handlePopupChange.bind(this));
        this.popupManager.initialize();
      } catch (error) {
        console.error("Failed to initialize PopupManager:", error);
      }
    },
    /**
     * 打开外部链接
     */
    onOpenParentLink(e) {
      // 打开外部链接（监管条款）
      const url = e.currentTarget.dataset.url;
      this.onConfirmOpenLinkPopup({
        data: { type: "parent", id: "123", url },
      });
    },

    /**
     * 确认首充奖励弹窗
     */
    onConfirmDepositPopup() {
      this.onConfirmOpenLinkPopup({
        data: { type: "Bet", id: "123" },
      });
      this.closeDepositPopup();
    },

    /**
     * 关闭首充奖励弹窗
     */
    closeDepositPopup() {
      if (this.props.onCloseDepositPopup) {
        this.props.onCloseDepositPopup();
      }
    },
    /**
     * 处理弹窗变化
     * @param {Object|null} popup - 弹窗对象，null表示关闭所有弹窗
     */
    handlePopupChange(popup) {
      const popupVisible = {
        Tip21Old: false,
        VipTip: false,
        VipCashback: false,
        ActivityBonus: false,
        RegisterBonus: false,
        BonusPopups: false,
      };

      // 设置当前弹窗可见
      if (popup && popup.id) {
        popupVisible[popup.id] = true;
      }

      this.setData({ popupVisible, currentPopup: popup });

      // 所有弹窗关闭时更新余额
      if (!popup) {
        setTimeout(() => {
          if (this.props.onUpdateAvailableCoins) {
            this.props.onUpdateAvailableCoins();
          }
        }, 1000);
      }
    },

    /**
     * 拒绝21岁条款
     */
    onReject21Terms() {
      this.openForbidAccess();
    },

    /**
     * 确认关闭弹窗
     */
    onConfirmClosePopup() {
      if (this.popupManager) {
        this.popupManager.closeCurrentPopup({ type: "close" });
      }
    },

    /**
     * 确认跳转
     */
    onConfirmOpenPopup() {
      if (this.data.currentPopup) {
        this.onConfirmOpenLinkPopup(this.data.currentPopup);
      }
    },

    /**
     * 确认领取奖励
     */
    onConfirmClaimPopup() {
      if (this.popupManager) {
        this.popupManager.closeCurrentPopup({ type: "claim" });
      }
    },

    /**
     * 处理跳转链接
     */
    onConfirmOpenLinkPopup(popup) {
      if (!popup || !popup.data) {
        console.error("Invalid popup data for link navigation");
        return;
      }

      this.setData({
        showJumpConfrim_1: true,
        jumpTypeInfo: {
          type: popup.data.type || "game",
          id: popup.data.id || "123",
        },
      });

      if (this.popupManager) {
        this.popupManager.closeCurrentPopup({ type: "close" });
      }
    },

    /**
     * 关闭跳转链接弹框
     */
    offOpenLinkDialog() {
      this.setData({ showJumpConfrim_1: false });
    },

    /**
     * 打开禁止访问弹框
     */
    openForbidAccess() {
      this.setData({ forbid_access: true });
    },

    /**
     * 关闭禁止访问弹框
     */
    forbidAccessDone() {
      this.setData({ forbid_access: false });
    },

    /**
     * 点击弹窗外部区域
     */
    onTapOverlay() {
      const currentPopup = this.data.currentPopup;
      if (currentPopup && currentPopup.allowClose) {
        if (this.popupManager) {
          this.popupManager.closeCurrentPopup({ closed: true });
        }
      }
    },

    /**
     * 打开条款页面
     */
    openTerms() {
      my.navigateTo({ url: "/pages/terms/terms" });
    },
  },
});
