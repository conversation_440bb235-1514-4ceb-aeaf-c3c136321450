import PopupManager from "./PopupManager";

/**
 * 活动弹窗组件 - 管理和显示各种活动弹窗
 */
Component({
  data: {
    currentPopup: null,
    popupVisible: {
      Tip21Old: false,
      VipTip: false,
      VipCashback: false,
      ActivityBonus: false,
      RegisterBonus: false,
      BonusPopups: false,
    },
    forbid_access: false,
    showJumpConfrim_1: false,
    jumpTypeInfo: { type: "game", id: "123" },
    depositAward: "",
    token: "",
  },

  props: {
    deposit_award: "",
    info: {
      token: "",
      isVip: false,
      register_award: {},
      is_register: 0,
    },
    onCloseDepositPopup: Function,
    onUpdateAvailableCoins: Function,
  },

  didMount() {
    this.initializePopupManager();
    this.setData({
      token: (this.props.info && this.props.info.token) || "",
      depositAward: this.props.deposit_award || "",
    });
  },

  didUpdate(prevProps) {
    const currentInfo = this.props.info || {};
    const prevInfo = prevProps.info || {};

    // 重新初始化条件：token变化或awardTypes变化
    if ((prevInfo.token !== currentInfo.token && currentInfo.token) || prevInfo.awardTypes !== currentInfo.awardTypes) {
      this.initializePopupManager();
    }

    // 更新数据
    this.setData({
      token: currentInfo.token || "",
      depositAward: this.props.deposit_award || "",
    });
  },

  methods: {
    /**
     * 初始化弹窗管理器
     */
    initializePopupManager() {
      if (!this.props.info || !this.props.info.token) return;

      try {
        this.popupManager = new PopupManager(this.props.info);
        this.popupManager.setPopupChangeCallback(this.handlePopupChange.bind(this));
        this.popupManager.initialize();
      } catch (error) {
        console.error("Failed to initialize PopupManager:", error);
      }
    },
    /**
     * 打开外部链接
     */
    onOpenParentLink(e) {
      const url = e.currentTarget.dataset.url;
      this.onConfirmOpenLinkPopup({
        data: { type: "parent", id: "123", url },
      });
    },

    /**
     * 确认首充奖励弹窗
     */
    onConfirmDepositPopup() {
      this.onConfirmOpenLinkPopup({
        data: { type: "Bet", id: "123" },
      });
      this.closeDepositPopup();
    },

    /**
     * 关闭首充奖励弹窗
     */
    closeDepositPopup() {
      if (this.props.onCloseDepositPopup) {
        this.props.onCloseDepositPopup();
      }
    },
    /**
     * 处理弹窗变化
     */
    handlePopupChange(popup) {
      const popupVisible = {
        Tip21Old: false,
        VipTip: false,
        VipCashback: false,
        ActivityBonus: false,
        RegisterBonus: false,
        BonusPopups: false,
      };

      if (popup && popup.id) {
        popupVisible[popup.id] = true;
      }

      this.setData({ popupVisible, currentPopup: popup });

      // 所有弹窗关闭时更新余额
      if (!popup) {
        setTimeout(() => {
          if (this.props.onUpdateAvailableCoins) {
            this.props.onUpdateAvailableCoins();
          }
        }, 1000);
      }
    },

    /**
     * 拒绝21岁条款
     */
    onReject21Terms() {
      this.openForbidAccess();
    },

    /**
     * 确认关闭弹窗
     */
    onConfirmClosePopup() {
      if (this.popupManager) {
        this.popupManager.closeCurrentPopup({ type: "close" });
      }
    },

    /**
     * 确认跳转
     */
    onConfirmOpenPopup() {
      if (this.data.currentPopup) {
        this.onConfirmOpenLinkPopup(this.data.currentPopup);
      }
    },

    /**
     * 确认领取奖励
     */
    onConfirmClaimPopup() {
      if (this.popupManager) {
        this.popupManager.closeCurrentPopup({ type: "claim" });
      }
    },

    /**
     * 处理跳转链接
     */
    onConfirmOpenLinkPopup(popup) {
      if (!popup || !popup.data) {
        console.error("Invalid popup data for link navigation");
        return;
      }

      this.setData({
        showJumpConfrim_1: true,
        jumpTypeInfo: {
          type: popup.data.type || "game",
          id: popup.data.id || "123",
        },
      });

      if (this.popupManager) {
        this.popupManager.closeCurrentPopup({ type: "close" });
      }
    },

    /**
     * 关闭跳转链接弹框
     */
    offOpenLinkDialog() {
      this.setData({ showJumpConfrim_1: false });
    },

    /**
     * 打开禁止访问弹框
     */
    openForbidAccess() {
      this.setData({ forbid_access: true });
    },

    /**
     * 关闭禁止访问弹框
     */
    forbidAccessDone() {
      this.setData({ forbid_access: false });
    },

    /**
     * 点击弹窗外部区域
     */
    onTapOverlay() {
      const currentPopup = this.data.currentPopup;
      if (currentPopup && currentPopup.allowClose) {
        if (this.popupManager) {
          this.popupManager.closeCurrentPopup({ closed: true });
        }
      }
    },

    /**
     * 打开条款页面
     */
    openTerms() {
      my.navigateTo({ url: "/pages/terms/terms" });
    },
  },
});
