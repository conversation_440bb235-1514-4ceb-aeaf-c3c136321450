export const enum EnumPopUpPrefabURL {
    VipTip,//升级VIP提示
    RegisterBonus,//注册/绑定奖励
    ActivityBonus,//所有活动奖励 反水之类的 必须领取才会获得 lemoon
}

//priority越大，越先弹出
const HallAutoPopConfig = {
    [EnumPopUpPrefabURL.VipTip]: { priority: 12 },
    [EnumPopUpPrefabURL.ActivityBonus]: { priority: 11 },
    [EnumPopUpPrefabURL.RegisterBonus]: { priority: 5 },
}

export const AutoPopMgr = (function () {
    let is_showing = false;
    function checkNextPopDialog(popName) {
        let bPop = false;
        let realPopName = Number(popName);
        switch (realPopName) {
            case EnumPopUpPrefabURL.VipTip:
                break; 
            case EnumPopUpPrefabURL.ActivityBonus:
                break;  
            case EnumPopUpPrefabURL.RegisterBonus:
                break;    
        }
        return { bPop, realPopName };
    }
    return {
        autoPopupDialog() {
            if(this.is_showing)return;
            //增加排序
            //每次找到最大的
            let result_true:null | {bPop:boolean,realPopName:number} = null;
            let max_priority = 0;
            for (let type in HallAutoPopConfig) {
                let key = HallAutoPopConfig[type]
                let result = checkNextPopDialog(type);
                if(result.bPop){
                    if(key.priority > max_priority){
                        //找到最高级的 先弹出
                        result_true = result
                        max_priority = key.priority// 修复bug 排序没有生效
                    }
                }
            }
            if(result_true){
                this.showQueuePopup(result_true.realPopName)
            }else{
                this.is_showing = false;
            }
        },
        //判断还有没有弹窗
        has_pops(){
            if(this.is_showing)return true;
            let result_true = false;
            let max_priority = 0;
            for (let type in HallAutoPopConfig) {
                let key = HallAutoPopConfig[type]
                let result = checkNextPopDialog(type);
                if(result.bPop){
                    if(key.priority > max_priority){
                        //找到最高级的 先弹出
                        result_true = true
                        max_priority = key.priority// 修复bug 排序没有生效
                    }
                }
            }
            return result_true;
        },
        // 显示弹框
        showQueuePopup: function (curPopType) {
            switch (curPopType) {
                case EnumPopUpPrefabURL.VipTip:
                    this.is_showing = true;
                    break;
                case EnumPopUpPrefabURL.ActivityBonus:
                    this.is_showing = true;
                    break;
                case EnumPopUpPrefabURL.RegisterBonus:
                    this.is_showing = true;
                    break;    
                default:
                    this.is_showing = false;
                    break;
            }
        },
        set_isshowing(isshow = false){
            this.is_showing = isshow
        },
        // 销毁当前弹框，弹出下个弹框
        destroyQueuePopup: function () {
            this.is_showing = false;
            let self = this;
            setTimeout(() => {
                self.autoPopupDialog();
            }, 100);
            
        },
    }
})();