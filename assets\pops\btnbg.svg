<svg width="229" height="56" viewBox="0 0 229 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_ii_1628_6407)">
<rect width="228" height="56" rx="28" fill="url(#paint0_linear_1628_6407)"/>
</g>
<rect x="1.5" y="1.5" width="225" height="53" rx="26.5" stroke="#FFDFBF" stroke-width="3"/>
<mask id="mask0_1628_6407" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="229" height="56">
<rect x="2.46875" y="1.5" width="225" height="53" rx="26.5" fill="url(#paint1_linear_1628_6407)" stroke="#FFDFBF" stroke-width="3"/>
</mask>
<g mask="url(#mask0_1628_6407)">
<g opacity="0.5" filter="url(#filter1_f_1628_6407)">
<ellipse cx="124.781" cy="56" rx="91" ry="8" fill="#FFF1B4" style="mix-blend-mode:overlay"/>
</g>
<g filter="url(#filter2_f_1628_6407)">
<path d="M198.781 4C210.932 4 220.781 13.8497 220.781 26C220.781 26.5042 220.762 27.0043 220.729 27.5C219.957 16.0494 210.427 7 198.781 7H30.7812C18.631 7 8.78125 16.8497 8.78125 29C8.78125 35.3014 11.4312 40.9833 15.6768 44.9941C9.15905 41.1755 4.78125 34.0996 4.78125 26C4.78125 13.8497 14.631 4 26.7812 4H198.781Z" fill="white"/>
</g>
<g filter="url(#filter3_f_1628_6407)">
<path d="M211.844 15.0479C218.802 17.5366 223.781 24.1862 223.781 32C223.781 41.8514 215.867 49.8525 206.05 49.9961C214.025 47.3659 219.781 39.856 219.781 31C219.781 24.4853 216.665 18.6995 211.844 15.0479ZM7.78711 31.5107C7.94139 37.666 10.8779 43.1296 15.3848 46.6943C10.7844 43.4336 7.78125 38.0679 7.78125 32C7.78125 31.8364 7.78277 31.6733 7.78711 31.5107Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_ii_1628_6407" x="0" y="-2" width="228" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.882368 0 0 0 0 0 0 0 0 0 0.204451 0 0 0 1 0"/>
<feBlend mode="multiply" in2="shape" result="effect1_innerShadow_1628_6407"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.258324 0 0 0 0 0.220974 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1628_6407" result="effect2_innerShadow_1628_6407"/>
</filter>
<filter id="filter1_f_1628_6407" x="3.78125" y="18" width="242" height="76" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="15" result="effect1_foregroundBlur_1628_6407"/>
</filter>
<filter id="filter2_f_1628_6407" x="-3.21875" y="-4" width="232" height="56.9941" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_1628_6407"/>
</filter>
<filter id="filter3_f_1628_6407" x="1.78125" y="9.04785" width="228" height="46.9482" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="3" result="effect1_foregroundBlur_1628_6407"/>
</filter>
<linearGradient id="paint0_linear_1628_6407" x1="114" y1="11.5294" x2="114" y2="53.1176" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF1E35"/>
<stop offset="1" stop-color="#FF916C"/>
</linearGradient>
<linearGradient id="paint1_linear_1628_6407" x1="114.969" y1="11.5294" x2="114.969" y2="53.1176" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF1E35"/>
<stop offset="1" stop-color="#FF916C"/>
</linearGradient>
</defs>
</svg>
