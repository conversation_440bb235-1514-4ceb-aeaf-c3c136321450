// 提现风控弹窗
Component({
  mixins: [],
  data: {
    visible: false,
    needBet: 0,
    showCurrentBet: 0,
    showTotalBet: 0,
    progressWidth: '0%'  // 添加进度条宽度变量
  },
  props: {
    currentBet: 0,
    totalBet: 0,
    onCancel: () => {},
    onConfirm: () => {}
  },
  didMount() {
    this.calculateProgress();
  },
  
  didUpdate(prevProps) {
    // 当 currentBet 或 totalBet 变化时重新计算进度
    if (prevProps.currentBet !== this.props.currentBet || 
        prevProps.totalBet !== this.props.totalBet) {
      this.calculateProgress();
    }
  },
  didUnmount() {},
  methods: {
    calculateProgress() {
      const currentBet = this.props.currentBet || 0;
      const totalBet = this.props.totalBet || 0;
      // 保留两位小数
      const needBet = (totalBet - currentBet).toFixed(2)
      this.setData({
        needBet: needBet,
        showCurrentBet: currentBet.toFixed(2),
        showTotalBet: totalBet.toFixed(2)
      })
      const width = totalBet ? (currentBet / totalBet * 100) : 0;
      this.setData({
        progressWidth: width + '%'
      });
    },
    show() {
      this.setData({
        visible: true
      });
      this.calculateProgress();
    },
    hide() {
      this.setData({
        visible: false
      });
    },
    handleCancel() {
      const { onCancel } = this.props;
      this.hide();
      onCancel();
    },
    handleConfirm() {
      const { onConfirm } = this.props;
      this.hide();
      onConfirm();
    }
  },
});
