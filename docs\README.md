# 项目文档索引

本目录包含了 GCash Mini-Game 项目的所有技术文档和重构说明。

## 📚 文档列表

### 🚀 HTTP 请求优化文档

- [**HTTP 请求优化使用指南**](./archive/HTTP请求优化使用指南.md) - HTTP 请求工具的详细使用指南
- [**my.request 优化方案总结**](./archive/my.request优化方案总结.md) - HTTP 请求优化的完整技术方案

### 📋 详细技术文档

- [**环境配置说明**](./archive/环境配置说明.md) - 环境配置的详细使用说明和故障排除
- [**交易记录重构说明**](./archive/交易记录重构说明.md) - 交易记录重构的详细技术文档

## 🎯 重构概览

### 1. HTTP 请求优化重构 ✅

**完成时间**: 2025-08-04
**目标**: 统一 HTTP 请求管理，提升代码质量和可维护性

**主要成果**:

- 创建分层架构：HTTP 客户端 → 请求管理器 → API 服务层
- 实现智能错误处理和自动重试机制
- 支持灵活的 token 配置（请求头/数据/双重）
- 提供业务级 API 封装，简化常用操作
- 保持完全向后兼容，支持渐进式迁移

**影响文件**: 4 个新增核心文件 + 多个页面优化

### 3. 环境配置重构 ✅

**完成时间**: 2025-07-08
**目标**: 统一管理环境配置，简化上线前切换流程

**主要成果**:

- 提取了 `list.js`、`http.js`、`launch.js` 中的所有 URL 配置
- 创建统一的 `utils/config.js` 配置文件
- 支持 dev/prod/test 三种环境一键切换
- 提供自动化切换脚本

**影响文件**: 3 个核心文件 + 4 个新增文件

### 4. 交易记录重构 ✅

**完成时间**: 2025-07-08
**目标**: 优化交易记录数据结构，提升用户体验

**主要成果**:

- 将混合存储改为按类型分别存储（充值、提现、奖励）
- 实现数据隔离和持久化
- 减少重复网络请求
- 保持完全向后兼容

**影响范围**: 交易记录相关功能


## 🏗️ 技术架构

### HTTP 请求优化架构

```
HTTP 请求分层架构
├── 业务层 (Pages/Components)
│   ├── pages/game/list.js
│   ├── components/activityPops/PopupManager.js
│   └── ...
├── API 服务层 (API Service)
│   ├── utils/api-service.js      # 业务 API 封装
│   └── utils/api-endpoints.js    # API 端点配置
├── 请求管理层 (Request Manager)
│   └── utils/request-manager.js  # 请求管理和错误处理
├── HTTP 客户端层 (HTTP Client)
│   └── utils/http.js             # 基础 HTTP 客户端
└── 底层请求 (Native Request)
    └── my.request                # 支付宝小程序原生请求
```

### 环境配置架构

```
项目根目录
├── utils/
│   ├── config.js          # 统一配置管理
│   ├── http.js            # HTTP 请求工具
│   └── launch.js          # 启动配置
├── pages/
│   └── game/
│       └── list.js        # 游戏列表页面
└── scripts/
    ├── switch-env.js      # 环境切换脚本
    └── deploy.js          # 部署脚本
```

## 🔧 快速参考

### HTTP 请求优化使用

```javascript
// 1. 使用 apiService（推荐）
import { apiService } from "/utils/api-service.js";

// 登录
const result = await apiService.login(authCode, this, marketChannel);

// 获取用户信息
const userInfo = await apiService.getUserInfo(token, this);

// 获取跑马灯消息
const marquee = await apiService.getMarqueeList(this);

// 2. 使用 httpClient（灵活配置）
import { httpClient } from "/utils/http.js";

// Token 在请求头中
const response = await httpClient.requestWithTokenHeader("api/player/info", token, {}, { baseUrl: "common" });

// Token 同时在数据和请求头中
const secureResponse = await httpClient.requestWithTokenBoth("api/secure/endpoint", token, { data: "value" }, { baseUrl: "common" });
```

### 环境切换

```bash
# 修改 utils/config.js 中的 ENVIRONMENT 变量
const ENVIRONMENT = 'prod';  // 'dev' | 'prod' | 'test'

# 或使用脚本切换
node scripts/switch-env.js prod
```

### 配置参数

| 环境 | baseUrl                         | 说明             |
| ---- | ------------------------------- | ---------------- |
| dev  | `https://pre.nustaronline.vip/` | 开发环境（预发） |
| prod | `https://io.nustargame.com/`    | 生产环境         |
| test | 测试环境配置                    | 测试环境         |

### 交易记录数据结构

```javascript
transactionData: {
  topup: { list: [], groupedList: [], pageNum: 1, isNoMoreData: false },
  withdrawal: { list: [], groupedList: [], pageNum: 1, isNoMoreData: false },
  reward: { list: [], groupedList: [], pageNum: 1, isNoMoreData: false }
}
```

## 📋 上线前检查清单

### HTTP 请求优化

- [ ] 确认所有 API 端点配置正确（`utils/api-endpoints.js`）
- [ ] 测试 `apiService` 方法正常工作
- [ ] 验证 token 配置（请求头/数据）符合后端要求
- [ ] 检查错误处理和重试机制
- [ ] 确认请求日志和调试信息正常

### 环境配置

- [ ] `utils/config.js` 中 `ENVIRONMENT` 设置为 `'prod'`
- [ ] 测试登录功能
- [ ] 测试 API 请求
- [ ] 测试静态资源加载

### 功能测试

- [ ] 交易记录类型切换
- [ ] 分页加载功能
- [ ] 充值/提现功能
- [ ] 游戏启动功能
- [ ] 下载引导浮标显示/隐藏
- [ ] 浮标位置和动画效果
- [ ] 浮标点击跳转功能

## 🔗 相关链接

### 核心文件

- [项目主 README](../README.md) - 项目总体介绍
- [HTTP 客户端](../utils/http.js) - 基础 HTTP 请求工具
- [API 服务层](../utils/api-service.js) - 业务 API 封装
- [请求管理器](../utils/request-manager.js) - 请求管理和错误处理
- [API 端点配置](../utils/api-endpoints.js) - API 端点统一配置
- [环境配置文件](../utils/config.js) - 环境配置文件

### 工具和脚本

- [配置测试工具](../utils/config-test.js) - 配置测试工具
- [环境切换脚本](../scripts/switch-env.js) - 环境切换脚本

## 📞 技术支持

如遇问题，请按以下顺序查阅：

1. **HTTP 请求问题** → [HTTP 请求优化使用指南.md](./archive/HTTP请求优化使用指南.md)
2. **API 调用问题** → [my.request 优化方案总结.md](./archive/my.request优化方案总结.md)
3. **环境配置问题** → [环境配置说明.md](./archive/环境配置说明.md)
4. **交易记录问题** → [交易记录重构说明.md](./archive/交易记录重构说明.md)
5. **配置验证** → 使用 `utils/config-test.js` 工具
6. **错误排查** → 检查控制台错误信息和网络请求

---

**文档维护**: 请在进行重构或重要变更时及时更新相关文档
**最后更新**: 2025-08-04
