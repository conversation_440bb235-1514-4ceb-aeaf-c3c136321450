# my.request 优化方案总结

## 概述

本次优化针对项目中 `my.request` 方法的使用进行了全面的重构和优化，提供了更便捷、更安全、更易维护的 HTTP 请求解决方案。

## 优化目标

1. **统一配置管理**: 将 baseUrl、headers、token 等公共参数统一管理
2. **简化调用方式**: 减少重复代码，提供更简洁的 API
3. **增强错误处理**: 统一的错误处理机制，自动处理常见错误场景
4. **提高可维护性**: 模块化设计，便于后续维护和扩展
5. **保持兼容性**: 不破坏现有代码，支持渐进式迁移

## 架构设计

### 分层架构

```
┌─────────────────────────────────────┐
│           业务层 (Pages)              │
├─────────────────────────────────────┤
│        API 服务层 (api-service.js)    │
├─────────────────────────────────────┤
│      请求管理层 (request-manager.js)   │
├─────────────────────────────────────┤
│       HTTP 客户端 (http.js)          │
├─────────────────────────────────────┤
│        配置层 (config.js)            │
└─────────────────────────────────────┘
```

### 核心组件

1. **utils/http.js** - 基础 HTTP 客户端
2. **utils/request-manager.js** - 高级请求管理器
3. **utils/api-service.js** - 业务级 API 服务
4. **utils/api-endpoints.js** - API 端点配置
5. **utils/config.js** - 环境配置（已存在）

## 主要功能特性

### 1. 统一的配置管理

- **多 baseUrl 支持**: 自动选择 common、avt、main 等不同的 baseUrl
- **自动 headers 配置**: 统一添加 terminal 等必需的请求头
- **Token 自动管理**: 自动添加 token 到请求数据中

### 2. 简化的调用方式

```javascript
// 原有方式 (20+ 行代码)
my.request({
  url: this.data.baseUrl + "api/player/info",
  method: "POST",
  headers: { terminal: APP_DEVICE.terminal },
  data: { token: this.data.token },
  success: (res) => { /* 处理逻辑 */ },
  fail: (res) => { /* 错误处理 */ }
});

// 优化后方式 (3 行代码)
const response = await apiService.getUserInfo(this.data.token, this);
if (response.data?.code === 200) {
  // 处理逻辑
}
```

### 3. 智能错误处理

- **自动 Token 失效处理**: 检测到 token 失效自动重新获取授权
- **业务错误码映射**: 自动识别并处理各种业务错误
- **网络错误处理**: 统一的网络错误提示和重试机制
- **加载状态管理**: 自动显示/隐藏加载提示

### 4. 类型化的 API 管理

- **端点配置化**: 所有 API 端点统一配置管理
- **参数标准化**: 统一的请求参数构建
- **业务方法封装**: 常用业务操作的高级封装

## 文件结构

```
utils/
├── config.js              # 环境配置 (已存在)
├── http.js                # 基础 HTTP 客户端 (已优化)
├── request-manager.js     # 高级请求管理器 (新增)
├── api-service.js         # 业务级 API 服务 (新增)
└── api-endpoints.js       # API 端点配置 (新增)

docs/
├── HTTP请求优化使用指南.md    # 详细使用指南
└── my.request优化方案总结.md  # 本文档

examples/
└── request-optimization-examples.js  # 使用示例
```

## 使用示例

### 基础用法

```javascript
import { httpClient } from "/utils/http.js";

// 简单 GET 请求
const response = await httpClient.get("api/marquee/list", {
  baseUrl: "common"
});

// 带 token 的 POST 请求
const response = await httpClient.requestWithToken(
  "api/player/info",
  this.data.token,
  {},
  { baseUrl: "common", showLoading: true }
);
```

### 高级用法

```javascript
import apiService from "/utils/api-service.js";

// 用户登录
const result = await apiService.login(authCode, this, marketChannel);

// 获取用户信息
const response = await apiService.getUserInfo(this.data.token, this);

// 充值请求
const response = await apiService.deposit({
  amount: 100,
  award: 10,
  config_id: 1
}, this.data.token, this);

// 批量初始化用户数据
const result = await apiService.initUserData(this.data.token, this);
```

## 迁移策略

### 渐进式迁移

1. **第一阶段**: 新功能使用新的 API 服务
2. **第二阶段**: 逐步替换现有的关键接口
3. **第三阶段**: 全面替换所有 my.request 调用

### 兼容性保证

- 保留原有的 `request` 方法，确保现有代码正常运行
- 新旧方法可以并存，支持逐步迁移
- 错误处理向后兼容

## 性能优化

### 1. 请求优化

- **连接复用**: 统一的 HTTP 客户端实例
- **参数缓存**: 避免重复构建请求参数
- **智能重试**: 指数退避的重试策略

### 2. 代码优化

- **减少重复代码**: 统一的请求逻辑
- **模块化设计**: 按功能分离，便于维护
- **类型安全**: 明确的参数和返回值定义

## 错误处理机制

### 自动处理的错误类型

| 错误码 | 错误类型 | 处理方式 |
|--------|----------|----------|
| 401, 400, 100010 | Token 失效 | 自动重新获取授权 |
| 102121 | 系统维护 | 显示维护提示 |
| 1000051 | IP 限制 | 显示 IP 限制提示 |
| 102008, 101013 | 账号锁定 | 显示账号锁定信息 |
| 102043 | 未满 21 岁 | 显示年龄限制提示 |

### 错误处理流程

```
请求发起 → 网络检查 → 业务错误码检查 → 自动处理 → 返回结果
```

## 配置管理

### BaseUrl 类型

- `main`: 主要 API 接口
- `common`: 通用 API 接口  
- `avt`: 活动相关接口
- `www`: 网页相关
- `assets`: 静态资源

### 环境切换

通过修改 `utils/config.js` 中的 `ENVIRONMENT` 变量即可切换环境，所有请求工具会自动使用对应环境的配置。

## 最佳实践

1. **优先使用 API 服务层**: 对于常用业务操作，使用 `apiService` 提供的方法
2. **传入页面上下文**: 在页面方法中调用时，将 `this` 作为 context 参数传入
3. **合理使用加载提示**: 对于耗时操作设置 `showLoading: true`
4. **错误处理**: 使用 try-catch 包装异步请求，但大部分错误会被自动处理
5. **参数验证**: 在调用前验证必需的参数（如 token）

## 后续扩展

### 计划中的功能

1. **请求缓存**: 对于配置类接口添加缓存机制
2. **请求队列**: 管理并发请求，避免重复请求
3. **性能监控**: 添加请求性能监控和统计
4. **离线支持**: 网络异常时的离线数据支持

### 扩展指南

- 新增 API 端点: 在 `api-endpoints.js` 中添加配置
- 新增业务方法: 在 `api-service.js` 中添加对应方法
- 自定义错误处理: 在 `request-manager.js` 中扩展错误处理逻辑

## 总结

本次优化通过分层架构和模块化设计，大幅简化了 HTTP 请求的使用方式，提高了代码的可维护性和可扩展性。同时保持了良好的兼容性，支持渐进式迁移，为项目的长期发展奠定了坚实的基础。
