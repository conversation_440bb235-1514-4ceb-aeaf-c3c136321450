# HTTP 请求优化使用指南

## 概述

本文档介绍了项目中 `my.request` 方法的优化方案，提供了更便捷、更安全的 HTTP 请求工具。

## 优化内容

### 1. 基础 HTTP 客户端 (`utils/http.js`)

提供了统一的 HTTP 请求封装，支持：
- 多种 baseUrl 自动选择
- 自动 token 管理
- 统一的请求头配置
- 加载提示管理
- Promise 化的请求接口

### 2. 高级请求管理器 (`utils/request-manager.js`)

提供了业务级的请求管理，包含：
- 统一的错误处理
- Token 失效自动重试
- 网络错误处理
- 业务错误码映射
- 登录状态管理

## 使用方法

### 基础用法

#### 1. 简单请求

```javascript
import { httpClient } from "/utils/http.js";

// GET 请求
const response = await httpClient.get("api/user/info", {
  baseUrl: "common",
  token: this.data.token
});

// POST 请求
const response = await httpClient.post("api/user/update", {
  name: "<PERSON>",
  email: "<EMAIL>"
}, {
  baseUrl: "common",
  token: this.data.token,
  showLoading: true
});
```

#### 2. 使用快捷方法

```javascript
import { httpClient } from "/utils/http.js";

// 带 token 的请求
const response = await httpClient.requestWithToken(
  "api/player/info",
  this.data.token,
  {},
  { baseUrl: "common" }
);

// 带加载提示的请求
const response = await httpClient.requestWithLoading(
  "api/data/list",
  { page: 1, limit: 10 },
  { baseUrl: "avt" }
);
```

#### 3. 完整配置请求

```javascript
import { httpClient } from "/utils/http.js";

const response = await httpClient.request({
  url: "api/payment/balance-add",
  baseUrl: "common",
  method: "POST",
  data: {
    amount: 100,
    award: 10,
  },
  token: this.data.token,
  showLoading: true,
  headers: {
    "Custom-Header": "value"
  }
});
```

### 高级用法

#### 1. 使用请求管理器

```javascript
import requestManager from "/utils/request-manager.js";

// 在页面方法中使用
async getUserData() {
  try {
    const response = await requestManager.request({
      url: "api/player/info",
      baseUrl: "common",
      token: this.data.token,
      showLoading: true
    }, this); // 传入页面上下文用于错误处理

    if (response.data?.code === 200) {
      this.setData({
        userInfo: response.data.data
      });
    }
  } catch (error) {
    console.error("获取用户数据失败:", error);
  }
}
```

#### 2. 登录请求

```javascript
import requestManager from "/utils/request-manager.js";

async playLogin(authCode) {
  try {
    const result = await requestManager.login(authCode, this);
    
    if (result.success) {
      // 登录成功
      const userData = result.data;
      this.setData({
        token: userData.token,
        userInfo: userData.user_info
      });
      
      // 保存 token
      my.setStorage({
        key: "token",
        data: userData.token
      });
    } else {
      // 登录失败，错误已自动处理
      console.log("登录失败:", result);
    }
  } catch (error) {
    console.error("登录异常:", error);
  }
}
```

## BaseUrl 类型说明

| 类型 | 对应配置 | 用途 |
|------|----------|------|
| `main` | `baseUrl` | 主要 API 接口 |
| `common` | `baseUrl_common` | 通用 API 接口 |
| `avt` | `baseUrl_avt` | 活动相关接口 |
| `www` | `wwwUrl` | 网页相关 |
| `assets` | `assetsUrl` | 静态资源 |

## 错误处理

请求管理器会自动处理以下错误类型：

- **Token 失效** (401, 400, 100010): 自动清除 token 并重新获取授权
- **系统维护** (102121): 显示维护提示
- **IP 限制** (1000051): 显示 IP 限制提示
- **账号锁定** (102008, 101013): 显示账号锁定信息
- **未满 21 岁** (102043): 显示年龄限制提示
- **网络错误**: 显示网络错误提示

## 迁移指南

### 原有代码示例

```javascript
// 原有写法
my.request({
  url: this.data.baseUrl + "api/player/info",
  method: "POST",
  headers: {
    terminal: APP_DEVICE.terminal,
  },
  data: {
    token: this.data.token,
  },
  dataType: "json",
  success: (res) => {
    // 处理成功
  },
  fail: (res) => {
    // 处理失败
  },
});
```

### 优化后写法

```javascript
// 推荐写法
import requestManager from "/utils/request-manager.js";

try {
  const response = await requestManager.request({
    url: "api/player/info",
    baseUrl: "common",
    token: this.data.token,
    showLoading: true
  }, this);
  
  if (response.data?.code === 200) {
    // 处理成功
  }
} catch (error) {
  // 错误已自动处理
}
```

## 最佳实践

1. **优先使用请求管理器**: 对于业务请求，建议使用 `requestManager` 以获得完整的错误处理
2. **传入页面上下文**: 在页面方法中调用时，将 `this` 作为第二个参数传入
3. **合理使用加载提示**: 对于耗时操作设置 `showLoading: true`
4. **选择正确的 baseUrl**: 根据接口类型选择合适的 baseUrl 类型
5. **错误处理**: 使用 try-catch 包装异步请求，但大部分错误会被自动处理

## 注意事项

1. 新的工具保持了与原有 `request` 方法的兼容性
2. 建议逐步迁移，不需要一次性替换所有代码
3. 错误处理依赖页面上下文，确保传入正确的 `this` 对象
4. Token 管理是自动的，但需要确保页面有相应的错误处理方法（如 `getAuthCode`）
