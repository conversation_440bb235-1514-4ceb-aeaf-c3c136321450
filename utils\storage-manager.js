/*本地存储对应的管理类*/
//生成唯一不重复ID
function generateUuid(length = 8) {
  return Number(Math.random().toString().substring(3, length) + Date.now()).toString(36);
}

// 本地缓存对应的key
const StorageKey = {
  kDeviceId: 'kDeviceId',
}

// 存储值
function setValue(key, value) {
  // 异步存储值到本地
  my.setStorage({
    key: key,
    data: value,
  });
}

// 获取值
function getValue(key) {
  // 同步获取缓存的值
  return my.getStorageSync({
    key: key
  }).data;
}

// 获取device_id
function getDeviceId() {
  let device_id = getValue(StorageKey.kDeviceId);
  if (device_id == null) {
    device_id = 'gcash' + generateUuid();
    setValue(StorageKey.kDeviceId, device_id);
  }
  return device_id;
}
// 生成随机字符串
function randomString(length = 8) {
  return Math.random().toString(36).substring(2, 2 + length);
}
// 生成指定长度的数组
function generateMockData(length, defaultValue = {}, key) {
  return Array.from({
    length
  }, () => ({
    ...defaultValue,
    [key]: randomString(10)
  }));
}

export default {
  StorageKey,
  setValue,
  getValue,
  getDeviceId,
  generateMockData,
}