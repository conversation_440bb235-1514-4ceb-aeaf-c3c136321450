Component({
  properties: {
    title: String, // 标题
    message: String, // 提示消息
    showCancel: {
      type: Boolean,
      value: true // 是否显示取消按钮
    },
    cancelText: {
      type: String,
      value: '取消' // 取消按钮文本
    },
    confirmText: {
      type: String,
      value: '确认' // 确认按钮文本
    },
    isShow: {
      type: Boolean,
      value: false // 默认不显示
    }
  },

  methods: {
    // 显示提示框
    show(options = {}) {
      this.setData({
        ...options,
        isShow: true
      });
    },

    // 隐藏提示框
    hide() {
      this.setData({
        isShow: false
      });
    },

    // 确认按钮点击事件
    onConfirm() {
      this.triggerEvent('confirm'); // 触发确认事件
      this.hide();
    },

    // 取消按钮点击事件
    onCancel() {
      this.triggerEvent('cancel'); // 触发取消事件
      this.hide();
    }
  }
});