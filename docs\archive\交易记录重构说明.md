# 交易记录数据结构重构说明

## 📋 重构概述

将 `transactionHistoryList` 相关代码按照接口类别重构，改为对象分别存储三个数据来源，并对应修改视图展示。

## 🎯 重构目标

- ✅ 按接口类别分别存储三个数据来源（充值、提现、奖励）
- ✅ 避免切换类型时数据混乱
- ✅ 提高数据管理的清晰度和可维护性
- ✅ 保持向后兼容性

## 📊 数据结构变化

### 重构前
```javascript
data: {
  transactionHistoryList: [],        // 混合存储所有类型数据
  copyTransactionHistoryList: [],    // 提现记录临时存储
  acopyTransactionHistoryList: [],   // 充值/奖励记录临时存储
  curTransactionPageNum: 1,          // 当前页码
  isNoMoreData: false               // 是否没有更多数据
}
```

### 重构后
```javascript
data: {
  // 按接口类别分别存储三个数据来源
  transactionData: {
    // 充值记录 (activePicker: 0)
    topup: {
      list: [],           // 原始数据列表
      groupedList: [],    // 按日期分组的列表
      pageNum: 1,         // 当前页码
      isNoMoreData: false // 是否没有更多数据
    },
    // 提现记录 (activePicker: 1)
    withdrawal: {
      list: [],
      groupedList: [],
      pageNum: 1,
      isNoMoreData: false
    },
    // 奖励记录 (activePicker: 2)
    reward: {
      list: [],
      groupedList: [],
      pageNum: 1,
      isNoMoreData: false
    }
  },
  // 兼容性：保留原有字段，通过计算属性获取当前激活的数据
  transactionHistoryList: [],
  isNoMoreData: false,
  curTransactionPageNum: 1
}
```

## 🔧 新增辅助方法

### 1. `getCurrentTransactionType()`
根据 `activePicker` 获取当前激活的交易数据类型。

```javascript
getCurrentTransactionType() {
  const typeMap = {
    0: 'topup',      // 充值
    1: 'withdrawal', // 提现
    2: 'reward'      // 奖励
  };
  return typeMap[this.data.activePicker] || 'topup';
}
```

### 2. `getCurrentTransactionData()`
获取当前激活的交易数据对象。

### 3. `updateCurrentTransactionData(updates)`
更新当前激活的交易数据，同时同步兼容性字段。

### 4. `resetTransactionData(type)`
重置指定类型的交易数据。

### 5. `resetAllTransactionData()`
重置所有交易数据。

## 🔄 修改的方法

### 1. `getPopupTransactionRecords()`
- 使用 `resetAllTransactionData()` 重置所有数据
- 移除手动设置各种列表为空的代码

### 2. `getTransactionRecords(type)`
- 使用 `getCurrentTransactionData()` 获取当前数据
- 使用 `updateCurrentTransactionData()` 更新数据
- 自动管理页码递增

### 3. `getRedeemRecords()`
- 使用 `getCurrentTransactionData()` 获取当前数据
- 使用 `updateCurrentTransactionData()` 更新数据
- 自动管理页码递增

### 4. `handleTransactionScrollToLower()`
- 移除手动页码递增逻辑
- 页码管理由各个获取方法内部处理

### 5. `changeActivePicker(e)`
- 智能切换：如果目标类型已有数据则直接显示，否则重置
- 使用 `getCurrentTransactionData()` 和 `resetTransactionData()`

### 6. `changeActiveTimer(e)` 和 `changeActiveStatus(e)`
- 使用 `resetTransactionData()` 重置当前类型数据
- 移除手动清空多个列表的代码

## 📱 视图层兼容性

视图层无需修改，因为：
1. 保留了 `transactionHistoryList` 字段
2. 保留了 `isNoMoreData` 字段
3. 保留了 `curTransactionPageNum` 字段
4. 这些字段会自动同步当前激活类型的数据

## 🎯 重构收益

### 1. **数据隔离**
- 每种交易类型的数据独立存储
- 切换类型时不会丢失已加载的数据
- 避免数据混乱和重复请求

### 2. **性能优化**
- 切换到已加载过的类型时直接显示，无需重新请求
- 减少不必要的网络请求
- 提升用户体验

### 3. **代码清晰**
- 数据管理逻辑更清晰
- 减少重复代码
- 提高可维护性

### 4. **向后兼容**
- 视图层无需修改
- 现有的数据绑定继续有效
- 平滑过渡

## 🔍 数据流程

### 初始化流程
1. 用户打开交易记录弹窗
2. 调用 `getPopupTransactionRecords()`
3. 重置所有交易数据 `resetAllTransactionData()`
4. 默认加载充值记录

### 切换类型流程
1. 用户点击不同的交易类型标签
2. 调用 `changeActivePicker(e)`
3. 检查目标类型是否已有数据
4. 如有数据直接显示，如无数据则重置并请求

### 加载更多流程
1. 用户滚动到底部
2. 调用 `handleTransactionScrollToLower()`
3. 根据当前类型调用对应的获取方法
4. 自动管理页码和数据追加

### 筛选条件变更流程
1. 用户修改时间或状态筛选条件
2. 调用 `resetTransactionData()` 重置当前类型数据
3. 重新请求数据

## ✅ 测试要点

1. **类型切换测试**
   - 在充值页面加载数据后切换到提现，再切换回充值
   - 验证充值数据是否保留

2. **分页测试**
   - 在每个类型中测试上拉加载更多
   - 验证页码管理是否正确

3. **筛选测试**
   - 修改筛选条件后验证数据是否正确重置
   - 验证不同类型的筛选条件是否独立

4. **兼容性测试**
   - 验证视图显示是否正常
   - 验证所有交互功能是否正常

## 📞 注意事项

1. **数据同步**：兼容性字段会自动同步，无需手动管理
2. **内存管理**：长时间使用可能积累较多数据，可考虑添加数据清理机制
3. **错误处理**：网络错误时需要正确处理页码状态
4. **状态管理**：确保 `activePicker` 状态与实际显示数据一致
